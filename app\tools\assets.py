from langchain.tools import tool
from loguru import logger

from app.schemas.assets import (
    Assets,
    CurrentAssets,
    FixedAssets,
    IntangibleAssets,
    InvestmentAssets,
    TangibleFixedAssets,
)


@tool
def check_asset_sum(assets: Assets) -> str:  # noqa: C901
    """Check the accuracy of `assets`

    Args:
        assets (Assets): the instance to be check
    Return:
        str: the message indicates result of checking
    """

    logger.debug("Start check assets sum")

    current_assets_output: CurrentAssets = assets.current_assets
    fixed_assets_output: FixedAssets = assets.fixed_assets
    return_response: str = ""

    # Check current assets
    current_assets_check = check_current_assets(current_assets_output)

    # Check fixed assets
    fixed_assets_check = check_fixed_assets(fixed_assets_output)

    # Get current assets value
    current_assets_value = 0
    if current_assets_output and current_assets_output.ttl_current_assets:
        current_assets_value = current_assets_output.ttl_current_assets.value

    # Get fixed assets value
    fixed_assets_value = 0
    if fixed_assets_output and fixed_assets_output.fixed_assets:
        fixed_assets_value = fixed_assets_output.fixed_assets.value

    # Check total assets
    total_components_value = current_assets_value + fixed_assets_value
    reported_total_assets = assets.total_asset.value

    # Check if the sum of components equals the total assets
    tolerance = 0.01
    if abs(total_components_value - reported_total_assets) <= tolerance:
        return_response += f"\nThe total assets value is good ({total_components_value} vs {reported_total_assets}). -> validated."
    elif total_components_value < reported_total_assets:
        return_response += f"\nThe total assets value (extracted from financial report) is greater than the sum of current and fixed assets ({reported_total_assets} vs {total_components_value}). Please check these fields again, you might be missing some values."
    else:
        return_response += f"\nThe total assets value (extracted from financial report) is less than the sum of current and fixed assets ({reported_total_assets} vs {total_components_value}). Please check these fields again, you might have duplicated values."

    output = (
        return_response + "\n" + "\n".join([current_assets_check, fixed_assets_check])
    )
    logger.debug(f"Output of validation tool:\n {output}")
    return output


def check_fixed_assets(fixed_assets_output: FixedAssets) -> str:
    logger.debug("Checking for fixed assets")
    return_response = ""

    intangible_assets_check = check_intangible_fixed_assets(
        fixed_assets_output.intangible_assets
    )
    tangible_assets_check = check_tangible_fixed_assets(
        fixed_assets_output.tangible_fixed_assets
    )
    investment_assets_check = check_investment_assets(
        fixed_assets_output.investment_assets
    )
    return_response += "\n".join(
        [intangible_assets_check, tangible_assets_check, investment_assets_check]
    )

    # Get tangible assets value
    tangible_assets_value = 0
    if (
        fixed_assets_output.tangible_fixed_assets
        and fixed_assets_output.tangible_fixed_assets.ttl_tangible_fixed_assets
    ):
        tangible_assets_value = (
            fixed_assets_output.tangible_fixed_assets.ttl_tangible_fixed_assets.value
        )

    # Get intangible assets value
    intangible_assets_value = 0
    if (
        fixed_assets_output.intangible_assets
        and fixed_assets_output.intangible_assets.ttl_intangible_assets
    ):
        intangible_assets_value = (
            fixed_assets_output.intangible_assets.ttl_intangible_assets.value
        )

    # Get investment assets value
    investment_assets_value = 0
    if (
        fixed_assets_output.investment_assets
        and fixed_assets_output.investment_assets.ttl_investment_and_other_assets
    ):
        investment_assets_value = (
            fixed_assets_output.investment_assets.ttl_investment_and_other_assets.value
        )

    # Sum up all fixed assets components
    total_components_value = (
        tangible_assets_value + intangible_assets_value + investment_assets_value
    )
    reported_fixed_assets_value = fixed_assets_output.fixed_assets.value

    # Check if the sum of components equals the total fixed assets
    tolerance = 0.01
    if abs(total_components_value - reported_fixed_assets_value) <= tolerance:
        return_response += f"\nThe fixed assets value is good ({total_components_value} vs {reported_fixed_assets_value}). -> validated."
    elif total_components_value < reported_fixed_assets_value:
        return_response += f"\nThe fixed assets value (extracted from financial report) is greater than the sum of tangible, intangible, and investment assets ({reported_fixed_assets_value} vs {total_components_value}). Please check these fields again, you might be missing some values."
    else:
        return_response += f"\nThe fixed assets value (extracted from financial report) is less than the sum of tangible, intangible, and investment assets ({reported_fixed_assets_value} vs {total_components_value}). Please check these fields again, you might have duplicated values."

    logger.debug("Finish checking for fixed assets")
    return return_response


def check_current_assets(current_assets: CurrentAssets) -> str:
    """Return message after checking sum for current assets values"""
    return ""


def check_tangible_fixed_assets(tangible_fixed_assets: TangibleFixedAssets) -> str:
    """Return message after checking sum for tangible fixed assets values"""
    logger.debug("Checking tangible fixed assets")
    if not tangible_fixed_assets or not tangible_fixed_assets.ttl_tangible_fixed_assets:
        return "No tangible fixed assets data available to check."

    # Calculate sum of all tangible fixed asset components
    component_sum = 0
    components = []

    if tangible_fixed_assets.buildings_structures:
        component_sum += tangible_fixed_assets.buildings_structures.value
        components.append(
            f"Buildings & Structures: {tangible_fixed_assets.buildings_structures.value}"
        )

    if tangible_fixed_assets.ttl_machinery_fixtures:
        component_sum += tangible_fixed_assets.ttl_machinery_fixtures.value
        components.append(
            f"TTL Machinery & Fixtures: {tangible_fixed_assets.ttl_machinery_fixtures.value}"
        )

    if tangible_fixed_assets.lease_assets:
        component_sum += tangible_fixed_assets.lease_assets.value
        components.append(f"Lease Assets: {tangible_fixed_assets.lease_assets.value}")

    if tangible_fixed_assets.land:
        component_sum += tangible_fixed_assets.land.value
        components.append(f"Land: {tangible_fixed_assets.land.value}")

    if tangible_fixed_assets.construction_in_progress:
        component_sum += tangible_fixed_assets.construction_in_progress.value
        components.append(
            f"Construction in Progress: {tangible_fixed_assets.construction_in_progress.value}"
        )

    if tangible_fixed_assets.other_tangible_fixed_assets:
        component_sum += tangible_fixed_assets.other_tangible_fixed_assets.value
        components.append(
            f"Other Tangible Fixed Assets: {tangible_fixed_assets.other_tangible_fixed_assets.value}"
        )

    # Handle accumulated depreciation separately as it's typically a negative value
    if tangible_fixed_assets.accumulated_depreciation:
        #     component_sum += tangible_fixed_assets.accumulated_depreciation.value
        components.append(
            f"Accumulated Depreciation: {tangible_fixed_assets.accumulated_depreciation.value} (not calculate in sum)"
        )

    total_value = tangible_fixed_assets.ttl_tangible_fixed_assets.value

    message = "Checking tangible fixed assets:\n"
    message += "Components:\n- " + "\n- ".join(components) + "\n"
    message += f"Sum of components: {component_sum}\n"
    message += f"Total tangible fixed assets: {total_value}\n"

    # Tolerance for floating point comparison
    tolerance = 0.01
    if abs(component_sum - total_value) <= tolerance:
        message += "The tangible fixed assets sum is correct."
    elif component_sum < total_value:
        message += f"Warning: The sum of tangible fixed asset components ({component_sum}) is less than the total ({total_value}). Some components may be missing or undervalued."
    else:
        message += f"Warning: The sum of tangible fixed asset components ({component_sum}) is greater than the total ({total_value}). Some components may be duplicated or overvalued."

    logger.debug("Finish checking tangible fixed assets")
    return message


def check_intangible_fixed_assets(intangible_assets: IntangibleAssets) -> str:
    """Return message after checking sum for intangible fixed assets values"""
    logger.debug("Checking intangible fixed assets")
    if not intangible_assets or not intangible_assets.ttl_intangible_assets:
        return "No intangible fixed assets data available to check."

    # Calculate sum of all intangible asset components
    component_sum = 0
    components = []

    if intangible_assets.goodwill:
        component_sum += intangible_assets.goodwill.value
        components.append(f"Goodwill: {intangible_assets.goodwill.value}")

    if intangible_assets.other_intangible_fixed_assets:
        component_sum += intangible_assets.other_intangible_fixed_assets.value
        components.append(
            f"Other Intangible Fixed Assets: {intangible_assets.other_intangible_fixed_assets.value}"
        )

    total_value = intangible_assets.ttl_intangible_assets.value

    message = "Checking intangible fixed assets:\n"
    message += "Components:\n- " + "\n- ".join(components) + "\n"
    message += f"Sum of components: {component_sum}\n"
    message += f"Total intangible fixed assets: {total_value}\n"

    # Tolerance for floating point comparison
    tolerance = 0.01
    if abs(component_sum - total_value) <= tolerance:
        message += "The intangible fixed assets sum is correct."
    elif component_sum < total_value:
        message += f"Warning: The sum of intangible fixed asset components ({component_sum}) is less than the total ({total_value}). Some components may be missing or undervalued."
    else:
        message += f"Warning: The sum of intangible fixed asset components ({component_sum}) is greater than the total ({total_value}). Some components may be duplicated or overvalued."

    logger.debug("Finish checking in-tangible fixed assets")
    return message


def check_investment_assets(investment_assets: InvestmentAssets) -> str:
    """Return message after checking sum for investment and other assets values"""
    logger.debug("Checking investment and other assets")
    if not investment_assets or not investment_assets.ttl_investment_and_other_assets:
        return "No investment and other assets data available to check."

    # Calculate sum of all investment components
    component_sum = 0
    components = []

    if investment_assets.sercurities_n_capital_investments:
        component_sum += investment_assets.sercurities_n_capital_investments.value
        components.append(
            f"Securities & Capital Investments: {investment_assets.sercurities_n_capital_investments.value}"
        )

    if investment_assets.capital_investment_of_subsidiaries_affiliates:
        component_sum += (
            investment_assets.capital_investment_of_subsidiaries_affiliates.value
        )
        components.append(
            f"Capital Investment of Subsidiaries & Affiliates: {investment_assets.capital_investment_of_subsidiaries_affiliates.value}"
        )

    if investment_assets.long_term_loans_receivable:
        component_sum += investment_assets.long_term_loans_receivable.value
        components.append(
            f"Long-term Loans Receivable: {investment_assets.long_term_loans_receivable.value}"
        )

    if investment_assets.lt_loans_accounts_receivable_from_related_co:
        component_sum += (
            investment_assets.lt_loans_accounts_receivable_from_related_co.value
        )
        components.append(
            f"LT Loans & Accounts Receivable from Related Co: {investment_assets.lt_loans_accounts_receivable_from_related_co.value}"
        )

    if investment_assets.doubtful_receivables:
        component_sum += investment_assets.doubtful_receivables.value
        components.append(
            f"Doubtful Receivables: {investment_assets.doubtful_receivables.value}"
        )

    if investment_assets.long_term_prepaid_expenses:
        component_sum += investment_assets.long_term_prepaid_expenses.value
        components.append(
            f"Long-term Prepaid Expenses: {investment_assets.long_term_prepaid_expenses.value}"
        )

    if investment_assets.real_estate_for_investment:
        component_sum += investment_assets.real_estate_for_investment.value
        components.append(
            f"Real Estate for Investment: {investment_assets.real_estate_for_investment.value}"
        )

    if investment_assets.lease_n_guarantee_deposits:
        component_sum += investment_assets.lease_n_guarantee_deposits.value
        components.append(
            f"Lease & Guarantee Deposits: {investment_assets.lease_n_guarantee_deposits.value}"
        )

    if investment_assets.deferred_tax_assets:
        component_sum += investment_assets.deferred_tax_assets.value
        components.append(
            f"Deferred Tax Assets: {investment_assets.deferred_tax_assets.value}"
        )

    # Allowance for doubtful accounts is typically negative, so it should be subtracted
    if investment_assets.allowance_for_doubtful_accounts:
        component_sum -= abs(investment_assets.allowance_for_doubtful_accounts.value)
        components.append(
            f"Allowance for Doubtful Accounts: {investment_assets.allowance_for_doubtful_accounts.value} (subtracted)"
        )

    if investment_assets.other_investment_and_lt_assets:
        component_sum += investment_assets.other_investment_and_lt_assets.value
        components.append(
            f"Other Investment & LT Assets: {investment_assets.other_investment_and_lt_assets.value}"
        )

    total_value = investment_assets.ttl_investment_and_other_assets.value

    message = "Checking investment and other assets:\n"
    if components:
        message += "Components:\n- " + "\n- ".join(components) + "\n"
    else:
        message += "No specific investment assets components found.\n"

    message += f"Sum of components: {component_sum}\n"
    message += f"Total investment and other assets: {total_value}\n"

    # Tolerance for floating point comparison
    tolerance = 0.01
    if abs(component_sum - total_value) <= tolerance:
        message += "The investment and other assets sum is correct."
    elif component_sum < total_value:
        message += f"Warning: The sum of investment and other asset components ({component_sum}) is less than the total ({total_value}). Some components may be missing or undervalued."
    else:
        message += f"Warning: The sum of investment and other asset components ({component_sum}) is greater than the total ({total_value}). Some components may be duplicated or overvalued."

    logger.debug("Finish checking investment and other assets")
    return message
