import regex as re
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, HumanMessagePromptTemplate
from langchain_openai import AzureChatOpenAI
from loguru import logger
from pydantic import BaseModel, Field
from typing_extensions import List, Literal

from app.data_processing.data_extraction import FinancialPDFExtractor
from app.schemas.table_mapping import BalanceSheetTable
from app.table_matching.prompt import (
    HUMAN_PROMPT,
    SYSTEM_PROMPT,
    balance_sheet_items,
    batch_classify_prompt,
    income_statement_items,
)
from app.utils.constants import MAX_RETRIES, RETRY_DELAY, FSConstants
from app.utils.utils import retry_on_rate_limit


class FinancialTemplate(BaseModel):
    template: Literal["Balance Sheet", "Income Statement", "None"] = Field(
        description="The most relevant Excel template to the asset names. Select one of these three options only: Balance Sheet, Income Statement, or None."
    )


class BatchFinancialTemplate(BaseModel):
    results: List[FinancialTemplate] = Field(
        description="A list of template matching results for each table in the batch"
    )


class FinancialTemplateMatcher:
    """
    A class to match financial asset names to template items (Balance Sheet or Income Statement)
    using an LLM
    """

    def __init__(self, llm: AzureChatOpenAI, batch_size: int = 20):
        """
        Initialize the matcher with lists of Balance Sheet and Income Statement items and specify the model.

        Args:
            llm: The Azure OpenAI LLM instance
            batch_size: Number of tables to process in a single API call (default: 20)
        """
        self.balance_sheet_items = balance_sheet_items
        self.income_statement_items = income_statement_items
        self.llm = llm
        self.batch_llm = llm.with_structured_output(BatchFinancialTemplate)
        self.table_extractor = FinancialPDFExtractor()

    def _create_prompt(self, asset_name):
        """
        Create a prompt (as a system and user message for ChatCompletion) to find the best match for the asset_name.
        The prompt provides context of possible items and asks for a JSON output with match and category.
        """
        asset_list_str = "- " + "\n- ".join(asset_name)

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=SYSTEM_PROMPT),
                HumanMessagePromptTemplate.from_template(
                    HUMAN_PROMPT, template_format="jinja2"
                ),
            ]
        ).format(asset_name=asset_list_str)
        return prompt

    async def ainvoke(self, asset_name: List[str]):
        """
        Use the OpenAI API to get the best match for a single asset name.
        Returns a dictionary with the asset, matched item, category, and confidence.
        """
        prompts = self._create_prompt(asset_name)
        try:
            # Call the OpenAI ChatCompletion API
            response = self.llm.invoke(prompts)
            return response
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise RuntimeError(f"OpenAI API call failed: {e}")

    @retry_on_rate_limit(
        retries=MAX_RETRIES, default_wait=RETRY_DELAY, max_wait=RETRY_DELAY
    )
    async def process_single_table(self, table: dict):
        """Process a single table and return template matching results with retry mechanism"""
        try:
            response = await self.ainvoke(table["itemname"])
            if response.template != "None":
                logger.info(f"Matched Template: {response.template}")
            return {
                "table_id": table["table_id"],
                "table": table["table"],
                "template": response.template,
            }
        except Exception as e:
            logger.error(f"Error processing table: {e}")
            return {
                "table_id": table["table_id"],
                "table": table["table"],
                "template": "None",
            }

    async def extract_text_columns(self, table_markdown: str) -> List[str]:
        """
        Extract text-only columns from a markdown table.
        Args:
        table_markdown : str
            A string containing the markdown representation of a table.
            Expected to follow standard markdown table format with pipes (|) as separators.
        Returns:
        list of str
            A flat list containing all cell values from columns that contain only text.
        """
        # Extract only the rows (lines starting with "|")
        rows = [
            line for line in table_markdown.strip().split("\n") if line.startswith("|")
        ]
        # Skip the header and alignment row
        data_rows = rows[2:]  # First two rows are header and separator
        # Convert markdown rows to 2D list
        table_data = [
            row.split("|")[1:-1] for row in data_rows
        ]  # remove leading/trailing pipe
        # Transpose to get columns
        transposed = list(zip(*table_data))

        text_only_cols = []
        for col in transposed:
            cleaned = [cell.strip() for cell in col if cell.strip()]
            if all(re.search(r"\p{L}", val) for val in cleaned) and len(cleaned) > 0:
                text_only_cols.extend(cleaned)
        return text_only_cols

    async def match_excel_template(self, table_markdown: str):
        """
        Extract tables from a PDF file and match them to the Balance Sheet template.
        Uses batching for efficient processing.

        Args:
            table_markdown (str): Markdown-formatted string of tables extracted from the PDF.

        Returns:
            bs_table_markdown (str): Combined markdown content of identified balance sheet tables.
            bs_ids (list[int]): List of table indices identified as balance sheet tables.
            detail_table_markdown_by_category (dict[str, str]): Mapping of detail table category names to their markdown content.
        """

        with_structured_llm = self.llm.with_structured_output(BalanceSheetTable)
        table_md_list = table_markdown.split(FSConstants.TABLE_SEPARATOR.value)

        # Break markdown into batches of 20 tables
        table_batches = [
            table_md_list[i : i + 20] for i in range(0, len(table_md_list), 20)
        ]

        bs_ids = []
        detail_table_number_ids = []
        category_map = {}

        def process_detail_ids(detail_ids_obj):
            for category, table_id_list in detail_ids_obj.dict().items():
                for table_id in table_id_list:
                    index = int(table_id.split("/")[-1])
                    detail_table_number_ids.append(index)
                    category_map[index] = category

        if len(table_batches) > 1:
            list_messages = [
                [
                    SystemMessage(content=SYSTEM_PROMPT),
                    HumanMessage(
                        content=batch_classify_prompt.format(
                            table_markdown=FSConstants.TABLE_SEPARATOR.value.join(batch)
                        )
                    ),
                ]
                for batch in table_batches
            ]

            _abatch_output: List[BalanceSheetTable] = await with_structured_llm.abatch(
                list_messages
            )

            for _output in _abatch_output:
                bs_ids.extend([int(x.split("/")[-1]) for x in _output.table_ids])
                process_detail_ids(_output.detail_table_ids)

        else:
            _output: BalanceSheetTable = await with_structured_llm.ainvoke(
                [
                    SystemMessage(content=SYSTEM_PROMPT),
                    HumanMessage(
                        content=batch_classify_prompt.format(
                            table_markdown=table_markdown
                        )
                    ),
                ]
            )

            bs_ids = [int(x.split("/")[-1]) for x in _output.table_ids]
            process_detail_ids(_output.detail_table_ids)

        # Build balance sheet markdown
        bs_table_markdown = "\n\n".join([table_md_list[i] for i in bs_ids])

        # Build categorized detail table markdown
        detail_table_by_category = {
            category: [] for category in set(category_map.values())
        }
        for idx in detail_table_number_ids:
            category = category_map[idx]
            detail_table_by_category[category].append(table_md_list[idx])

        detail_table_markdown_by_category = {
            k: "\n\n".join(v) if v else "" for k, v in detail_table_by_category.items()
        }

        # Logging
        # logger.debug(f"BALANCE SHEET: {len(bs_ids)}\n{bs_table_markdown}")
        # for cat, md in detail_table_markdown_by_category.items():
        #     logger.debug(f"DETAIL [{cat.upper()}]:\n{md}")

        return bs_table_markdown, bs_ids, detail_table_markdown_by_category
