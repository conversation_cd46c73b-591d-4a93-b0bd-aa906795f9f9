import regex as re
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, HumanMessagePromptTemplate
from langchain_openai import AzureChatOpenAI
from loguru import logger
from pydantic import BaseModel, Field
from typing_extensions import List, Literal

from app.data_processing.data_extraction import FinancialPDFExtractor
from app.schemas.table_mapping import BalanceSheetTable
from app.table_matching.prompt import (
    HUMAN_PROMPT,
    SYSTEM_PROMPT,
    balance_sheet_items,
    batch_classify_prompt,
    income_statement_items,
)
from app.utils.constants import MAX_RETRIES, RETRY_DELAY, FSConstants
from app.utils.utils import retry_on_rate_limit


class FinancialTemplate(BaseModel):
    template: Literal["Balance Sheet", "Income Statement", "None"] = Field(
        description="The most relevant Excel template to the asset names. Select one of these three options only: Balance Sheet, Income Statement, or None."
    )


class BatchFinancialTemplate(BaseModel):
    results: List[FinancialTemplate] = Field(
        description="A list of template matching results for each table in the batch"
    )


class FinancialTemplateMatcher:
    """
    A class to match financial asset names to template items (Balance Sheet or Income Statement)
    using an LLM
    """

    def __init__(self, llm: AzureChatOpenAI, batch_size: int = 20):
        """
        Initialize the matcher with lists of Balance Sheet and Income Statement items and specify the model.

        Args:
            llm: The Azure OpenAI LLM instance
            batch_size: Number of tables to process in a single API call (default: 20)
        """
        self.balance_sheet_items = balance_sheet_items
        self.income_statement_items = income_statement_items
        self.llm = llm
        self.batch_llm = llm.with_structured_output(BatchFinancialTemplate)
        self.table_extractor = FinancialPDFExtractor()

    def _create_prompt(self, asset_name):
        """
        Create a prompt (as a system and user message for ChatCompletion) to find the best match for the asset_name.
        The prompt provides context of possible items and asks for a JSON output with match and category.
        """
        asset_list_str = "- " + "\n- ".join(asset_name)

        prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=SYSTEM_PROMPT),
                HumanMessagePromptTemplate.from_template(
                    HUMAN_PROMPT, template_format="jinja2"
                ),
            ]
        ).format(asset_name=asset_list_str)
        return prompt

    async def ainvoke(self, asset_name: List[str]):
        """
        Use the OpenAI API to get the best match for a single asset name.
        Returns a dictionary with the asset, matched item, category, and confidence.
        """
        prompts = self._create_prompt(asset_name)
        try:
            # Call the OpenAI ChatCompletion API
            response = self.llm.invoke(prompts)
            return response
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise RuntimeError(f"OpenAI API call failed: {e}")

    @retry_on_rate_limit(
        retries=MAX_RETRIES, default_wait=RETRY_DELAY, max_wait=RETRY_DELAY
    )
    async def process_single_table(self, table: dict):
        """Process a single table and return template matching results with retry mechanism"""
        try:
            response = await self.ainvoke(table["itemname"])
            if response.template != "None":
                logger.info(f"Matched Template: {response.template}")
            return {
                "table_id": table["table_id"],
                "table": table["table"],
                "template": response.template,
            }
        except Exception as e:
            logger.error(f"Error processing table: {e}")
            return {
                "table_id": table["table_id"],
                "table": table["table"],
                "template": "None",
            }

    async def extract_text_columns(self, table_markdown: str) -> List[str]:
        """
        Extract text-only columns from a markdown table.
        Args:
        table_markdown : str
            A string containing the markdown representation of a table.
            Expected to follow standard markdown table format with pipes (|) as separators.
        Returns:
        list of str
            A flat list containing all cell values from columns that contain only text.
        """
        # Extract only the rows (lines starting with "|")
        rows = [
            line for line in table_markdown.strip().split("\n") if line.startswith("|")
        ]
        # Skip the header and alignment row
        data_rows = rows[2:]  # First two rows are header and separator
        # Convert markdown rows to 2D list
        table_data = [
            row.split("|")[1:-1] for row in data_rows
        ]  # remove leading/trailing pipe
        # Transpose to get columns
        transposed = list(zip(*table_data))

        text_only_cols = []
        for col in transposed:
            cleaned = [cell.strip() for cell in col if cell.strip()]
            if all(re.search(r"\p{L}", val) for val in cleaned) and len(cleaned) > 0:
                text_only_cols.extend(cleaned)
        return text_only_cols

    async def match_excel_template(self, table_markdown: str):
        """
        Extract tables from a PDF files and match the appropriate template (Balance Sheet or Income Statement) to each table.
        Uses batching mechanism similar to income statement filtering with abatch for parallel processing.

        Args:
            table_markdown (str): Markdown-formated string of tables extracted from the PDF.
        Returns:
            results (list(dict)): A list of dictionaries containing table IDs and their matched templates.
        """

        with_structured_llm = self.llm.with_structured_output(
            BalanceSheetTable,
        )
        table_md_list = table_markdown.split(FSConstants.TABLE_SEPARATOR.value)

        # Break markdown by 20 tables per batch
        # TODO: Break batches by tokens (for example, 10k tokens per batch)
        table_batches = [
            table_md_list[i : i + 20] for i in range(0, len(table_md_list), 20)
        ]

        # Handle the case where there are multiple batches of tables
        if len(table_batches) > 1:
            list_messages = [
                [
                    SystemMessage(content=SYSTEM_PROMPT),
                    HumanMessage(
                        content=batch_classify_prompt.format(
                            table_markdown=FSConstants.TABLE_SEPARATOR.value.join(
                                table_md
                            )
                        )
                    ),
                ]
                for table_md in table_batches
            ]

            _abatch_output: List[BalanceSheetTable] = await with_structured_llm.abatch(
                list_messages
            )
            bs_ids = [
                int(x.split("/")[-1])
                for _aivoke_output in _abatch_output
                for x in _aivoke_output.table_ids
            ]
        else:
            _aivoke_output: BalanceSheetTable = await with_structured_llm.ainvoke(
                [
                    SystemMessage(content=SYSTEM_PROMPT),
                    HumanMessage(
                        content=batch_classify_prompt.format(
                            table_markdown=table_markdown
                        )
                    ),
                ]
            )

            bs_ids = [int(x.split("/")[-1]) for x in _aivoke_output.table_ids]

        table_markdown_list: list[str] = table_markdown.split("\n\n")
        bs_table_markdown = "\n\n".join([table_markdown_list[i] for i in bs_ids])

        return bs_table_markdown, bs_ids
