import json
import traceback
from typing import List, Optional

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    HTTPException,
    Request,
    UploadFile,
    status,
)
from loguru import logger
from sqlalchemy import false
from sqlalchemy.orm import Session

from app.database.db import (
    common_parameters,
    get_db,
    insert_row,
    search_filter_sort_paginate,
)
from app.features.conversation.model import (
    ConversationInfo,
    ConversationItemUpdate,
    ConversationResponse,
    ConversationUpdate,
    FinancialConversation,
    Status,
)
from app.features.conversation.service import ConversationService
from app.utils.constants import Message

conversation_router = APIRouter()
conversation_service = ConversationService()


@conversation_router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=ConversationResponse,
)
async def create_conversation(
    request: Request,
    user_id: int,
    conversation_name: str,
    # upload_file: UploadFile = File(...),
    db_session: Session = Depends(get_db),
    # _=Depends(require_group(GroupPermission.BP_ANALYSIS_AGENT.name)), # TODO: Enable when adding new group, category in main backend DB
):
    try:
        if request.state.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=Message.MSG_PERMISSION_DENIED,
            )

        financial_conversation = insert_row(
            db_session=db_session,
            obj_table=FinancialConversation(
                user_id=user_id,
                conversation_name=conversation_name,
                # document_id=financial_document.id,
                status=Status.INIT.value,
            ),
        )
        response = ConversationResponse(
            id=financial_conversation.id,
            user_id=financial_conversation.user_id,
            conversation_name=financial_conversation.conversation_name,
            status=financial_conversation.status,
            deleted=financial_conversation.deleted,
            created_at=financial_conversation.created_time,
            updated_at=financial_conversation.updated_time,
        )
        return response
    except HTTPException as e:
        logger.error(f"Error while uploading files and creating a new conversation {e}")
        raise e
    except Exception as e:
        logger.error(f"Error while uploading files and creating a new conversation {e}")
        logger.debug(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=Message.INTERNAL_SERVER_ERROR,
        ) from e


@conversation_router.get("", status_code=status.HTTP_200_OK)
def get_all_conversations_by_user(
    request: Request,
    common: dict = Depends(common_parameters),
):
    user_id = request.state.user_id
    try:
        query = (
            common["db_session"]
            .query(FinancialConversation)
            .filter(
                FinancialConversation.user_id == user_id,
                FinancialConversation.deleted.is_(False),
            )
        )
        # search by query
        if common["query_str"]:
            common["filter_spec"].append(
                {
                    "field": "conversation_name",
                    "op": "ilike",
                    "value": "%" + common["query_str"] + "%",
                }
            )

        expected_fields = [
            "id",
            "conversation_name",
            "created_time",
            "updated_time",
            "status",
            "deleted",
        ]
        return search_filter_sort_paginate(
            model=FinancialConversation.__tablename__,
            query=query,
            **common,
            expected_fields=expected_fields,
        )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error while getting conversations for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Cannot retrieve conversations for the given user",
        ) from e


@conversation_router.put("/{conversation_id}", status_code=status.HTTP_200_OK)
def update_conversation_by_id(
    request: Request,
    conversation_id: int,
    conversation_update: ConversationUpdate,
    db_session: Session = Depends(get_db),
):
    user_id = request.state.user_id
    try:
        conversation = conversation_service.update_conversation_by_id(
            user_id, conversation_id, conversation_update, db_session
        )
        if conversation is None:
            return {"detail": f"Conversation {conversation_id} not found."}
        conversation.mapping_metadata = (
            json.loads(conversation.mapping_metadata)
            if conversation.mapping_metadata
            else None
        )
        return conversation
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error while getting conversations for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cannot update the conversation {conversation_id} for the given user",
        ) from e


@conversation_router.delete("/{conversation_id}", status_code=status.HTTP_200_OK)
def delete_conversation_by_id(
    request: Request,
    conversation_id: int,
    db_session: Session = Depends(get_db),
):
    user_id = request.state.user_id
    try:
        is_deleted = conversation_service.delete_conversation_by_id(
            user_id, conversation_id, db_session
        )
        if is_deleted:
            return {"detail": f"Conversation {conversation_id} is deleted."}
        else:
            logger.error(f"Error while getting conversations for user {user_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Errors when deleting {conversation_id} for the given user",
            )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error while getting conversations for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cannot update the conversation {conversation_id} for the given user",
        ) from e


### Processing PDF files


@conversation_router.post("/process", status_code=status.HTTP_200_OK)
async def process_financial_statement(
    request: Request,
    user_id: int,
    conversation_id: int,
    upload_file: UploadFile = File(...),
    db_session: Session = Depends(get_db),
):
    # check conversation and pdf file
    file_name = upload_file.filename
    if not file_name or not file_name.lower().endswith((".pdf")):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=Message.MSG_INVALID_UPLOAD_FILE,
        )

    # check if the conversation exists
    conversation = (
        db_session.query(FinancialConversation)
        .filter(
            FinancialConversation.id == conversation_id,
            FinancialConversation.user_id == request.state.user_id,
        )
        .first()
    )
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=Message.MSG_CONVERSATION_NOT_FOUND,
        )

    # handle upload file
    conversation_info: ConversationInfo = (
        await conversation_service.process_financial_statement(
            upload_file=upload_file,
            conversation=conversation,
            user_id=user_id,
            db_session=db_session,
            user_email=request.state.email,
        )
    )
    # read and extract file content
    return conversation_info


@conversation_router.post("/process_v2", status_code=status.HTTP_200_OK)
async def process_financial_statement_v2(
    request: Request,
    user_id: int,
    background_tasks: BackgroundTasks,
    upload_files: Optional[List[UploadFile]] = File(
        default=None
    ),  # TODO: Change into Optional parameter
    conversation_id: Optional[int] = None,
    db_session: Session = Depends(get_db),
):
    # Create conversation
    conversation_response = await conversation_service.process_financial_statement_v2(
        user_id=user_id,
        db_session=db_session,
        user_email=request.state.email,
        background_tasks=background_tasks,
        upload_files=upload_files,
        conversation_id=conversation_id,
    )

    return conversation_response


@conversation_router.get(
    "/process", status_code=status.HTTP_200_OK, response_model=List[ConversationInfo]
)
def get_all_process_by_user(
    request: Request,
    db_session: Session = Depends(get_db),
    conv_status: str = Status.PROCESS.value,
) -> List[ConversationInfo]:
    user_id = request.state.user_id
    try:
        result = conversation_service.get_process_by_user(
            db_session, user_id, conv_status
        )

        logger.info(result)

        return result
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error while getting conversations for user {user_id}: {e}")
        raise HTTPException(
            status_code=(
                e.status_code
                if hasattr(e, "status_code")
                else status.HTTP_500_INTERNAL_SERVER_ERROR
            ),
            detail=(
                e.detail
                if hasattr(e, "detail")
                else "Cannot retrieve conversations for the given user"
            ),
        ) from e


@conversation_router.get("/{conversation_id}", status_code=status.HTTP_200_OK)
def get_conversation_detail_by_id(
    request: Request,
    conversation_id: int,
    db_session: Session = Depends(get_db),
):
    user_id = request.state.user_id

    # check conversation exists and not deleted
    conversation = (
        db_session.query(FinancialConversation)
        .filter(
            FinancialConversation.id == conversation_id,
            # FinancialConversation.user_id == request.state.user_id,
            FinancialConversation.deleted == false(),
        )
        .first()
    )
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=Message.MSG_CONVERSATION_NOT_FOUND,
        )

    # check if the user is the owner of the conversation
    if conversation.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=Message.MSG_PERMISSION_DENIED,
        )

    try:
        conversation = conversation_service.get_conversation_detail_by_id(
            conversation_id=conversation_id, db_session=db_session
        )
        if conversation is not None:
            return conversation
        else:
            return {"detail": f"Conversation {conversation_id} not found."}
    except Exception as e:
        logger.error(f"Error while getting conversations for user {user_id}: {e}")


@conversation_router.put("/{conversation_id}/items", status_code=status.HTTP_200_OK)
def update_item_values_by_conversation_id(
    request: Request,
    conversation_id: int,
    conversation_item_update: ConversationItemUpdate,
    db_session: Session = Depends(get_db),
):
    user_id = request.state.user_id
    try:
        conversation = conversation_service.update_item_values_by_conversation_id(
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_item_update=conversation_item_update,
            db_session=db_session,
        )
        if conversation is None:
            return {"detail": f"Conversation {conversation_id} not found."}
        return conversation
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error while getting conversations for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cannot update the conversation {conversation_id} for the given user",
        ) from e
