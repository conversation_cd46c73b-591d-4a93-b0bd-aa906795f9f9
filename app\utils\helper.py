import json
import re
from collections import defaultdict
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from loguru import logger
from pydantic import BaseModel

from app.schemas.base import NewItem, RecalculationUnit
from app.schemas.highlights import BoundingBox, OutputItem
from app.utils.constants import recalculation_rules as default_recalculation_rules


def print_value(value: float):
    """convert float to string for better read, like
    100000000.0 to 100,000,000
    345567.01 to 345,567.01
    12.130012 to 12.130012
    0.01 to 0.01
    12.000 to 12
    12345 to 12,345
    """
    if value == 0:
        return "0"
    if value % 1 == 0:
        return f"{int(value):,}"
    else:
        return f"{value:,.2f}"


def convert_bounding_boxes_to_highlight_format(
    bounding_boxes: list[BoundingBox],
) -> list[dict]:
    """
    Convert a list of BoundingBox objects to the specified highlight format

    Args:
        item: The OutputItem containing bounding boxes

    Returns:
        List of dictionaries representing the highlight format
    """
    rects: list[BoundingBox] = []
    for bbox in bounding_boxes:
        # Create rects list from the bounding boxes

        rect = rotate_bbox(bbox)
        rects.append(rect)
    if rects == []:
        return []

    highlight = {
        "content": {"text": ""},
        "position": {
            "boundingRect": rects[0],
            "rects": rects,
        },
        "comment": "",
        "id": bounding_boxes[0].gen_id(),  # Generate a unique ID
    }

    return [highlight]


# this function helps to extract all keys from a pydantic model, for example IncomeStatement
# to get the keys like "operating_metrics/gross_sales" for mapping with the json template
def get_keys_from_pydantic_model(
    model_class_or_instance: Any,
    prefix: str = "",
    separator: str = "/",
    # verbose: bool = False,
    stop_model: Any = NewItem,
) -> list[str]:
    """
    Extract all keys from a Pydantic model class or instance, including nested keys from sub-classes.

    Args:
        model_class_or_instance (Any): The Pydantic model class or instance to extract keys from.
        prefix (str, optional): The prefix to be added to each key. Defaults to "".
        separator (str, optional): The separator to be used between keys and nested keys. Defaults to "/".
        verbose (bool, optional): Whether to print debug information. Defaults to False.

    Returns:
        List[str]: A list of keys, including nested keys with proper hierarchy like 'operating_metrics/gross_sales'.
    """
    keys = []

    # Determine if we have a class or an instance
    if isinstance(model_class_or_instance, type):
        # We have a class
        model_class = model_class_or_instance
        instance = None
        # if verbose:
        #     print(f"Processing class: {model_class.__name__}")
    else:
        # We have an instance
        model_class = model_class_or_instance.__class__
        instance = model_class_or_instance
        # if verbose:
        #     print(f"Processing instance of: {model_class.__name__}")

    # Get fields from the model class
    if hasattr(model_class, "model_fields"):  # Pydantic v2
        # if verbose:
        #     print(f"Processing Pydantic v2 model: {model_class.__name__}")
        fields_dict = model_class.model_fields
        field_items = [(name, field.annotation) for name, field in fields_dict.items()]
    elif hasattr(model_class, "__annotations__"):  # Pydantic v1 or regular type hints
        fields_dict = model_class.__annotations__
        field_items = list(fields_dict.items())
    else:
        # if verbose:
        #     print("No fields found in the model class.")
        return keys

    # Process each field
    for key, field_type in field_items:
        # Skip private fields
        if key.startswith("_"):
            continue

        # if verbose:
        #     print(f"Field: {key}, Type: {field_type}")

        # Format the current key with prefix
        current_key = key if not prefix else f"{prefix}{separator}{key}"

        # Get the field value if we have an instance
        field_value = getattr(instance, key) if instance else None

        # Check if the field is a nested Pydantic model
        is_nested_model = False
        nested_model_type = None

        # Handle Union types (like NewItem | None)
        if hasattr(field_type, "__args__"):
            field_types = field_type.__args__
            non_none_types = [t for t in field_types if t is not type(None)]

            if non_none_types and len(non_none_types) > 0:
                if isinstance(non_none_types[0], type) and issubclass(
                    non_none_types[0], BaseModel
                ):
                    is_nested_model = True
                    nested_model_type = non_none_types[0]
                    # if verbose:
                    #     print(
                    # f"  - Found nested model in Union: {nested_model_type.__name__}"
                    # )

        elif isinstance(field_type, type) and issubclass(field_type, BaseModel):
            is_nested_model = True
            nested_model_type = field_type
            # if verbose:
            #     print(f"  - Found direct nested model: {nested_model_type.__name__}")
        if (
            is_nested_model
            and nested_model_type
            and not nested_model_type == stop_model
        ):
            # It's a nested model, recurse into it
            if field_value is not None:
                # if verbose:
                #     print(
                #     f"  - Found instance of nested model: {nested_model_type.__name__}"
                # )
                # Use the instance if available
                nested_keys = get_keys_from_pydantic_model(
                    field_value,
                    current_key,
                    separator,  # verbose
                )
                keys.extend(nested_keys)
            else:
                # if verbose:
                #     print(
                #     f"  - Found class of nested model: {nested_model_type.__name__}, otherwise use the class"
                # )
                # Otherwise use the class
                nested_keys = get_keys_from_pydantic_model(
                    nested_model_type,
                    current_key,
                    separator,  # verbose
                )
                keys.extend(nested_keys)
        else:
            # It's a primitive type or NewItem
            keys.append(current_key)
            # if verbose:
            #     print(f"  - Added key: {current_key}")

    return keys


def map_values_into_sheet(
    sheet_id: Union[int, str],
    item_list: list[OutputItem],
    template: Union[str, list[dict], None] = None,
) -> list[dict]:
    """
    Maps the values from output items into the correct sheet in the template.

    Args:
        sheet_id (Union[int, str]): The ID of the sheet to update.
        item_list (List[OutputItem]): List of output items containing values to map.
        template (Union[str, List[Dict], None], optional): Either a path to the template file,
            an already loaded template list, or None (defaults to app/template/template.json).

    Returns:
        List[Dict]: The updated template list with mapped values.
    """
    # Handle the template parameter
    if template is None:
        # Default template path
        template_path = "app/template/template.json"
        try:
            with open(template_path, "r") as f:
                template_data = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Template file not found at path: {template_path}")
        except json.JSONDecodeError:
            raise ValueError(
                f"Template file at {template_path} is not a valid JSON file"
            )
    elif isinstance(template, str):
        # A path was provided
        try:
            with open(template, "r") as f:
                template_data = json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Template file not found at path: {template}")
        except json.JSONDecodeError:
            raise ValueError(f"Template file at {template} is not a valid JSON file")
    elif isinstance(template, list):
        # Already loaded template list
        template_data = template
    else:
        raise TypeError("Template must be a file path, a list of dictionaries, or None")

    # Convert sheet_id to string for comparison
    sheet_id_str = str(sheet_id)

    # Find the target sheet
    target_sheet = None
    target_sheet_index = -1

    for idx, sheet in enumerate(template_data):
        if sheet["sheet_id"] == sheet_id_str:
            target_sheet = sheet
            target_sheet_index = idx
            break

    if target_sheet is None:
        raise ValueError(f"Sheet with ID {sheet_id} not found in template")

    # Create a lookup dictionary for faster item matching
    item_lookup = {item.id: item for item in item_list}

    # Update values in the sheet
    updated_count = 0

    # Process all screens in the sheet
    for screen in target_sheet["screens"]:
        # Update each item if it exists in the item_list
        for template_item in screen["items"]:
            if "item_id" in template_item and template_item["item_id"] in item_lookup:
                output_item = item_lookup[template_item["item_id"]]
                template_item["value"] = output_item.value
                template_item["bounding_region"] = output_item.bounding_regions
                updated_count += 1

    # Update the sheet in the template data
    template_data[target_sheet_index] = target_sheet

    logger.info(
        f"Updated {updated_count} items in sheet '{target_sheet['sheet_name']}'"
    )

    return template_data


def rotate_bbox(bounding_box: BoundingBox) -> dict:
    """
    Rotates a bounding box (x1, y1, x2, y2) based on the page rotation angle
    around the top-left corner (0, 0) of an image.

    Args:
        bounding_box (BoundingBox): An object with attributes:
            - x1, y1, x2, y2: Coordinates of the bounding box.
            - width (optional): Width of the image. Defaults to 100 if not present.
            - height (optional): Height of the image. Defaults to 100 if not present.
            - page_rotation (optional): Rotation angle in degrees (0, 90, 180, 270). Defaults to 0.
            - page_number (optional): Page number (for context). Defaults to 1.

    Returns:
        dict: A dictionary containing the rotated bounding box coordinates and other metadata.
    """

    # Extract bounding box attributes and handle defaults
    x1, y1, x2, y2 = bounding_box.x1, bounding_box.y1, bounding_box.x2, bounding_box.y2
    width = getattr(bounding_box, "width", 100)
    height = getattr(bounding_box, "height", 100)
    page_number = getattr(bounding_box, "page_number", 1)
    page_rotation = getattr(bounding_box, "page_rotation", 0)

    # Validate the rotation angle
    if page_rotation not in [0, 90, 180, 270]:
        raise ValueError("Page rotation must be one of 0, 90, 180, or 270 degrees.")

    # Rotation logic based on the page rotation angle
    if page_rotation == 0:
        x1_new, y1_new, x2_new, y2_new = x1, y1, x2, y2
    elif page_rotation == 90:
        x1_new, y1_new, x2_new, y2_new = y1, width - x2, y2, width - x1
    elif page_rotation == 180:
        x1_new, y1_new, x2_new, y2_new = (
            width - x2,
            height - y2,
            width - x1,
            height - y1,
        )
    elif page_rotation == 270:
        x1_new, y1_new, x2_new, y2_new = height - y2, x1, height - y1, x2

    # Normalize the rotated bounding box coordinates
    x1_final, y1_final = min(x1_new, x2_new), min(y1_new, y2_new)
    x2_final, y2_final = max(x1_new, x2_new), max(y1_new, y2_new)

    return {
        "x1": x1_final,
        "x2": x2_final,
        "y1": y1_final,
        "y2": y2_final,
        "width": width,
        "height": height,
        "pageNumber": page_number,
        "pageRotation": page_rotation,
    }


def postprocess(template: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filters out items from each screen in a list of sheets based on screen-specific limits
    and removes any items containing '_extracted' in their 'item_id'.

    Args:
        sheets (List[Dict[str, Any]]): List of sheet dictionaries. Each sheet contains:
            - 'sheet_id' (str)
            - 'sheet_name' (str)
            - 'screens' (List[Dict[str, Any]]): Each screen contains:
                - 'screen_id' (str)
                - 'screen_name' (str)
                - 'items' (List[Dict[str, Any]]): Each item contains 'item_id' (str)

    Returns:
        List[Dict[str, Any]]: New list of sheets with filtered items per screen name and
        without '_extracted' items.
    """
    screen_limits = {
        "Current Assets": 29,
        "Fixed Assets": 38,
        "Current Liabilities": 25,
        "Long-term Liabilities and Net Assets": 41,
        "OI": 8,
        "PAT": 30,
    }

    filtered_template = []
    for sheet in template:
        filtered_screens = []
        for screen in sheet.get("screens", []):
            screen_name = screen.get("screen_name", "")
            limit = screen_limits.get(screen_name)

            if limit is not None:
                items = screen.get("items", [])
                # Filter out items with '_extracted' in the item_id
                filtered_items = [
                    item
                    for item in items
                    if "_extracted" not in item.get("item_id", "")
                ][:limit]
                filtered_screen = {**screen, "items": filtered_items}
                filtered_screens.append(filtered_screen)

        filtered_template.append({**sheet, "screens": filtered_screens})

    return filtered_template


def build_item_lookup(
    template_data: List[Dict[str, Any]]
) -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Builds a lookup dictionary for template items based on sheet_id, screen_id, and item 'no'.

    Args:
        template_data (List[Dict[str, Any]]): The parsed JSON template data structure.

    Returns:
        Dict[str, Dict[str, Dict[str, Any]]]: A nested dictionary where the first key is sheet_id,
        the second key is screen_id, the third key is 'no', and the value is the corresponding item dictionary.
    """
    lookup = defaultdict(lambda: defaultdict(dict))  # sheet_id → screen_id → no → item
    for sheet in template_data:
        sheet_id = str(sheet.get("sheet_id"))
        for screen in sheet.get("screens", []):
            screen_id = str(screen.get("screen_id"))
            for item in screen.get("items", []):
                no = str(item.get("no"))
                lookup[sheet_id][screen_id][no] = item
    return lookup


def evaluate_expression(
    expr: List[str],
    lookup: Dict[str, Dict[str, Dict[str, Any]]],
    current_sheet: str,
    current_screen: str,
) -> Tuple[Optional[float], Optional[str], Optional[Dict[str, Any]]]:
    """
    Evaluates an arithmetic expression represented as a list of tokens, resolving item values
    from a nested lookup structure. Supports references to items on different screens.

    Args:
        expr (List[str]): Tokens forming the expression, e.g. ["1", "+", "2"] or ["screen1.5", "+", "3"].
        lookup (Dict): Nested dictionary (sheet_id → screen_id → no → item) containing item values.
        current_sheet (str): ID of the current sheet to resolve local references.
        current_screen (str): ID of the current screen to resolve local references.

    Returns:
        Tuple[Optional[float], Optional[str]]:
            - The computed result as float, or None if comparison fails.
            - An optional string message, empty if no issues, otherwise a string representation
              of the right-hand value or "None" if missing.
    """

    def get_item(token: str) -> Optional[Dict[str, Any]]:
        if token.startswith("screen") and "." in token[6:]:
            screen_id, no = token[6:].split(".", 1)
        else:
            screen_id, no = current_screen, token
        return lookup.get(current_sheet, {}).get(screen_id, {}).get(no)

    def get_raw_val(token: str) -> Any:
        item = get_item(token)
        return item.get("value") if item else None

    def get_val(token: str) -> float:
        val = get_raw_val(token)
        if isinstance(val, bool):
            return 1.0 if val else 0.0
        try:
            return float(val)
        except (TypeError, ValueError):
            return 0.0

    # Equality check
    if len(expr) == 3 and expr[1] == "==":
        left_val = get_val(expr[0])
        right_val = get_val(expr[2])
        source_item = get_item(expr[2])
        if source_item and source_item.get("value") is None:
            source_item = None

        if abs(right_val - left_val) >= 1e-2:
            return (
                (left_val, "None", source_item)
                if right_val is None
                else (left_val, str(right_val), source_item)
            )

        return left_val, "", source_item

    # Arithmetic evaluation
    expr_str = ""
    for token in expr:
        if re.fullmatch(r"[+\-*/()]|==", token):
            expr_str += f" {token} "
        else:
            expr_str += f"{get_val(token)} "

    try:
        result = eval(expr_str)
    except Exception:
        result = 0.0

    return result, "", None


def apply_update_rules(
    template_data: List[Dict[str, Any]], update_rules: Dict[str, Dict[str, List]]
) -> List[Dict[str, Any]]:
    """
    Applies calculation rules to update values in the template based on item 'no' arithmetic expressions.
    Populates the 'formula' field with 'coeffs', except when the expression is a comparison (==).

    Args:
        template_data (List[Dict[str, Any]]): The template data structure with sheets, screens, and items.
        update_rules (Dict[str, Dict[str, List]]): Nested dictionary where the first key is sheet_id, second is screen_id,
            and the value is a list of (target_no, expression_tokens) tuples.

    Returns:
        List[Dict[str, Any]]: The modified template data with calculated values.
    """
    lookup = build_item_lookup(template_data)

    def get_screen_and_no(token: str, current_screen: str) -> Tuple[str, str]:
        if token.startswith("screen") and "." in token[6:]:
            screen_part, no = token[6:].split(".", 1)
            return screen_part, no
        else:
            return current_screen, token

    def extract_coeffs(expr_tokens: List[str], current_screen: str) -> Dict[str, int]:
        coeffs = {}
        sign = 1
        for token in expr_tokens:
            if token == "+":
                sign = 1
            elif token == "-":
                sign = -1
            elif re.fullmatch(r"[*/()]|==", token):
                continue
            else:
                screen_id, no = get_screen_and_no(token, current_screen)
                key = f"{screen_id}|{no}"
                coeffs[key] = coeffs.get(key, 0) + sign
                sign = 1
        return coeffs

    for sheet_id, screens in update_rules.items():
        for screen_id, rules in screens.items():
            for target_no, expr_tokens in rules:
                try:
                    value, expected_value, source_item = evaluate_expression(
                        expr_tokens,
                        lookup,
                        current_sheet=sheet_id,
                        current_screen=screen_id,
                    )

                    target_item = (
                        lookup.get(sheet_id, {}).get(screen_id, {}).get(target_no)
                    )
                    if not target_item:
                        logger.debug(
                            f"Missing target item sheet {sheet_id}, screen {screen_id}, no {target_no}"
                        )
                        continue

                    # Update value
                    target_item["value"] = value

                    # Update expected_value and bounding_region
                    if expected_value != "":
                        target_item["expected_value"] = expected_value
                    if source_item and "bounding_region" in source_item:
                        target_item["bounding_region"] = source_item["bounding_region"]

                    # Skip formula if this is a comparison expression
                    if len(expr_tokens) == 3 and expr_tokens[1] == "==":
                        continue

                    coeffs = extract_coeffs(expr_tokens, screen_id)
                    target_item.setdefault("formula", {})["coeffs"] = coeffs

                except Exception as e:
                    logger.error(
                        f"Error evaluating {target_no} on sheet {sheet_id}, screen {screen_id}: {e}"
                    )

    return template_data


def recalculation_expression(
    expr: List[str],
    lookup: Dict[str, Dict[str, Dict[str, Any]]],
    current_sheet: str,
    current_screen: str,
    recalculation_rules: Optional[Dict[str, Dict[str, List]]] = None,
) -> Tuple[Optional[Dict[str, Any]], List[Dict[str, Any]]]:

    def get_item(token: str) -> Optional[Dict[str, Any]]:
        if token.startswith("screen") and "." in token[6:]:
            screen_id, no = token[6:].split(".", 1)
        else:
            screen_id, no = current_screen, token
        return lookup.get(current_sheet, {}).get(screen_id, {}).get(no)

    def is_extracted(item: Optional[Dict[str, Any]]) -> bool:
        return item and item.get("item_id", "").endswith("_extracted")

    target_expr, _ = expr

    target_item = get_item(target_expr)

    detail_exprs = recalculation_rules.get(current_sheet, {}).get(current_screen, [])
    detail_expr = None
    for target_no, expr_tokens in detail_exprs:
        if target_no == target_expr and "==" not in expr_tokens:
            detail_expr = expr_tokens
            break

    detail_items = [
        get_item(tok) for tok in detail_expr if not re.fullmatch(r"[+\-*/()]|==", tok)
    ]
    detail_items = [item for item in detail_items if item]

    return target_item, detail_items


def recalculation_builder(
    template_data: List[Dict[str, Any]],
    recalculation_rules: Optional[Dict[str, Dict[str, List]]] = None,
    allowed_sheets: Optional[Set[str]] = None,
    allowed_screens: Optional[Set[str]] = None,
) -> List[RecalculationUnit]:

    recalculation_rules = (
        default_recalculation_rules
        if recalculation_rules is None
        else recalculation_rules
    )
    allowed_sheets = {"1"} if allowed_sheets is None else allowed_sheets
    allowed_screens = {"1", "2"} if allowed_screens is None else allowed_screens

    lookup = build_item_lookup(template_data)
    results: List[RecalculationUnit] = []

    for sheet_id, screens in recalculation_rules.items():
        if sheet_id not in allowed_sheets:
            continue
        for screen_id, rules in screens.items():
            if screen_id not in allowed_screens:
                continue
            for expr in rules:
                # if len(expr) != 2 or "==" not in expr[1]:
                #     continue
                try:
                    target_item, detail_items = recalculation_expression(
                        expr=expr,
                        lookup=lookup,
                        current_sheet=sheet_id,
                        current_screen=screen_id,
                        recalculation_rules=recalculation_rules,
                    )

                    if not target_item or not detail_items:
                        continue

                    results.append(
                        RecalculationUnit(
                            target_item=target_item, detail_items=detail_items
                        )
                    )

                except Exception as e:
                    logger.error(
                        f"Error recalculationing expr {expr[1]} on sheet {sheet_id}, screen {screen_id}: {e}"
                    )

    return results
