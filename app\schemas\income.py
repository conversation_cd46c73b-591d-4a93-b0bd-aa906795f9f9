###
### This file hase already been deprecated.
### Please refer to the new file app/schemas/new_income.py for the updated code.
###
from pydantic import BaseModel, Field

from app.utils.helper import print_value

from .base import Item


class CoreOperatingMetrics(BaseModel):
    """
    This class defines core operating metrics that should be extracted from financial report.
    The core operating metrics includes:
    - Gross Sales (gross_sales): Total revenue from sales of goods and services before any deductions.
    - Cost of Sales (cost_of_sales): Direct costs attributable to the production of goods sold or services provided.
    - Gross Profit (gross_profit): The difference between gross sales and cost of sales.
    - Operating Expenses (operating_expenses): Total operating expenses of the company in the financial statement, which includes selling, general and administrative expenses, etc.
    - Operating Income (operating_income): The profit earned from core business operations, calculated as gross profit minus operating expenses.
    """

    gross_sales: Item = Field(
        description="Gross sales or total revenue from sales of goods and services before any deductions. (positive value)",
    )
    cost_of_sales: Item = Field(
        description="The cost of goods sold (COGS) or direct costs attributable to the production of goods sold or services provided. (positive value)",
    )
    gross_profit: Item = Field(
        description="The gross profit or the difference between gross sales and cost of sales. (positive value)",
    )
    operating_expenses: Item = Field(
        description="Total operating expenses of the company in the financial statement, which includes selling, general and administrative expenses, etc. (positive value)",
    )
    operating_income: float = Field(
        description="The operating income or the profit earned from core business operations, calculated as gross profit minus operating expenses. (positive value)",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class NonRecurringIncome(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to non-recurring income, which includes:
    - Interest Income (interest_income): Income earned from interest-bearing investments or loans.
    - Dividend Received (dividend_received): Income from dividends on equity investments.
    - Equity-Method Affiliate Profits (equity_method_affiliate_profits): Share of profits from investments in associates.
    - Foreign Exchange Gains (foreign_exchange_gains): Profits from currency exchange rate fluctuations.
    - Gain on Sales of Non-Current Assets (gain_on_sales_of_non_current_assets): Profits from selling fixed or long-term assets.
    - Gain on Sales of Investment & Securities (gain_on_sales_of_investment_securities): Profits from selling financial investments.
    - Gain from Asset Revaluation (gain_from_asset_revaluation): Profits from revaluing assets to fair market value.
    - Other Extraordinary Income (other_extraordinary_income): Other non-recurring income items not classified above.
    and Total Non-Recurring Income (ttl_non_recurring_income).

    Please note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    In case there are fields that not belong to pre-defined items, you can add it to 'Other Extraordinary Income'.
    """

    interest_income: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Interest Income'",
    )
    dividend_received: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Dividend Received'",
    )
    equity_method_affiliate_profits: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Equity-Method Affiliate Profits'",
    )
    foreign_exchange_gains: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Foreign Exchange Gains'",
    )
    gain_on_sales_of_non_current_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Gain on Sales of Non-Current Assets'",
    )
    gain_on_sales_of_investment_securities: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Gain on Sales of Investment & Securities'",
    )
    gain_from_asset_revaluation: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Gain from Asset Revaluation'",
    )
    other_extraordinary_income: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Extraordinary Income'",
    )
    ttl_non_recurring_income: Item = Field(
        description="The item about total non-recurring income"
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """return the string representation of the object.
        check for all values in the class, if value is None, then skip it.
        Otherwise, return the string of the value:
        - key: split by '_', capitalize first letter of each word, and join by space.
        - value (Item): print value of item, in str format, separated by comma, for example: '100,000', '200,000', '423,123.012', etc.

        Returns:
            str: the string representation of the object
        """
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class NonRecurringLoss(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to non-recurring loss, which includes:
    - Interest Expenses (interest_expenses): Costs incurred from borrowing money.
    - Equity-Method Affiliate Losses (equity_method_affiliate_losses): Share of losses from investments in associates.
    - Foreign Exchange Losses (foreign_exchange_losses): Losses from currency exchange rate fluctuations.
    - Loss on Sales & Disposal of Non-Current Assets (loss_on_sales_disposal_of_non_current_assets): Losses from selling fixed or long-term assets.
    - Loss on Sales of Investment & Securities (loss_on_sales_of_investment_securities): Losses from selling financial investments.
    - Loss from Asset Revaluation (loss_from_asset_revaluation): Losses from revaluing assets to fair market value.
    - Other Extraordinary Loss (other_extraordinary_loss): Other non-recurring loss items not classified above.
    and Total Non-Recurring Loss (ttl_non_recurring_loss).

    Please note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    In case there are fields that not belong to pre-defined items, you can add it to 'Other Extraordinary Loss', this final value should be total of components
    """

    interest_expenses: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Interest Expenses'",
    )
    equity_method_affiliate_losses: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Equity-Method Affiliate Losses'",
    )
    foreign_exchange_losses: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Foreign Exchange Losses'",
    )
    loss_on_sales_disposal_of_non_current_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Loss on Sales & Disposal of Non-Current Assets'",
    )
    loss_on_sales_of_investment_securities: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Loss on Sales of Investment & Securities'",
    )
    loss_from_asset_revaluation: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Loss from Asset Revaluation'",
    )
    other_extraordinary_loss: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Extraordinary Loss'",
    )
    ttl_non_recurring_loss: Item = Field(
        description="The item about total non-recurring loss"
    )

    # - Minority Interest (minority_interest): Share of profit attributable to minority shareholders.
    # - Discontinued Operation (discontinued_operation): Income or loss from discontinued business segments.
    # - Extraordinary Items (after Tax) (extraordinary_items_after_tax): Unusual and infrequent items after tax effects.

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class Income(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to net income, which includes:
    - Operating Income (operating_income): Income from core business operations.
    - Non-Recurring Income (non_recurring_income): Income from non-regular business activities.
    - Non-Recurring Loss (non_recurring_loss): Losses from non-regular business activities.
    - Income Taxes (income_taxes): Taxes levied on the company's income.
    - Net Income (net_income): The final profit after all deductions and adjustments.

    Please note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    """

    operating_metrics: CoreOperatingMetrics | None = Field(
        description="The value of the item in the financial statement that can map to 'Operating Income'",
    )
    non_recurring_income: NonRecurringIncome | None = Field(
        description="The value of the item in the financial statement that can map to 'Non-Recurring Income'",
    )
    non_recurring_loss: NonRecurringLoss | None = Field(
        description="The value of the item in the financial statement that can map to 'Non-Recurring Loss'",
    )
    income_taxes: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Income Taxes'",
    )
    # minority_interest: Item | None = Field(
    #     description="The value of the item in the financial statement that can map to 'Minority Interest'"
    # )
    # discontinued_operation: Item | None = Field( description="The value of the item in the financial statement that can map to 'Discontinued Operation'")
    # extraordinary_items_after_tax: Item | None = Field( description="The value of the item in the financial statement that can map to 'Extraordinary Items (after Tax)'")
    net_income: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Net Income'",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """return the string representation of the object.
        check for all values in the class, if value is None, then skip it.
        Otherwise, return the string of the value:
        - key: split by '_', capitalize first letter of each word, and join by space.
        - value (Item): print value of item, in str format, separated by comma, for example: '100,000', '200,000', '423,123.012', etc.

        Returns:
            str: the string representation of the object
        """
        data = [
            f"Core Operating Metrics:\n{self.operating_metrics.pretty_print(exchange_rate)}",
            f"Non-Recurring Income:\n{self.non_recurring_income.pretty_print(exchange_rate)}",
            f"Non-Recurring Loss:\n{self.non_recurring_loss.pretty_print(exchange_rate)}",
        ]
        data.append(
            f"Income Taxes: {print_value(self.income_taxes.value / exchange_rate)}"
            if self.income_taxes
            else "Income Taxes: None"
        )
        data.append(
            f"Net Income: {print_value(self.net_income.value / exchange_rate)}"
            if self.net_income
            else "Net Income: None"
        )

        return "\n\n".join(data)
