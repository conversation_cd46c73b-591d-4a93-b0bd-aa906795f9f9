import base64
import os
import re
import tempfile
from io import BytesIO
from mimetypes import guess_type
from pathlib import Path
from subprocess import PIPE, run
from typing import Dict, Union

import fitz
from azure.ai.documentintelligence.models import AnalyzeResult, DocumentParagraph
from langchain_text_splitters import CharacterTextSplitter
from loguru import logger
from PIL import Image

text_splitter = CharacterTextSplitter(chunk_size=650, chunk_overlap=250)
INCH_TO_PIXEL = 96


def get_width_height(result: AnalyzeResult, page_index: int):
    """
    page_index: start from 0
    """
    width, height = result.pages[page_index].width, result.pages[page_index].height
    return width, height


def get_coordinate(result: AnalyzeResult, paragraph: DocumentParagraph) -> Dict:
    points = paragraph.bounding_regions[0].polygon

    # Detect if points is a list of Point objects or flat array of coordinates (Old version DI)
    if hasattr(points[0], "x") and hasattr(points[0], "y"):
        # V1: Points are Point objects
        top_left = points[0]
        bottom_right = points[2]
        x1, y1 = top_left.x, top_left.y
        x2, y2 = bottom_right.x, bottom_right.y
        first_page = result.pages[0].page_number - 1
    else:
        # V2: Points are flat array of coordinates (New version DI)
        x1, y1 = points[0], points[1]
        x2, y2 = points[4], points[5]
        first_page = result.pages[0].get("pageNumber") - 1

    page_number = paragraph.bounding_regions[0].page_number
    pwidth, pheight = get_width_height(
        result=result, page_index=page_number - 1 - first_page
    )

    output = {
        "x1": x1 * INCH_TO_PIXEL,
        "x2": x2 * INCH_TO_PIXEL,
        "y1": y1 * INCH_TO_PIXEL,
        "y2": y2 * INCH_TO_PIXEL,
        "width": pwidth * INCH_TO_PIXEL,
        "height": pheight * INCH_TO_PIXEL,
        "pageNumber": page_number,
    }
    return output


def process_text(text):
    text = re.sub(r"\s{3,}", " ", text)
    text = re.sub(r"-{3,}", "-", text)
    text = re.sub(r"_{3,}", "-", text)
    return text


def update_coordinate(begin: float, end: float, total_length: float, coordinate: dict):
    """Update coordinate from percentage coordinate. Not work with pixel-based coordinate

    Args:
        begin (float): index of the beginning of the paragraph to be updated
        end (float): index of the ending of the paragraph to be updated
        total_length (float): total length of full paragraph
        coordinate (dict): old coordinate

    Returns:
        dict: new coordinate
    """
    my_coordinate = coordinate.copy()
    old_y1, old_y2 = my_coordinate["y1"], my_coordinate["y2"]
    y1 = begin / total_length * (old_y2 - old_y1) + old_y1
    y2 = end / total_length * (old_y2 - old_y1) + old_y1

    my_coordinate["y1"] = round(y1, 2)
    my_coordinate["y2"] = round(y2, 2)
    return my_coordinate


def preprocess_text(text):
    # Remove URLs
    def replace_fn(match_obj):
        return match_obj.group(1) + match_obj.group(3)

    text = re.sub(r"http\S+", "", text)

    # Remove HTML tags
    text = re.sub(r"<.*?>", "", text)

    # Remove CSS styles
    text = re.sub(r"\.mw-parser-output.*?\{.*?\}", "", text, flags=re.DOTALL)

    # Remove tabs and newlines
    text = re.sub(r"[\t\n]", " ", text)

    # Remove punctuation and special characters
    # text = re.sub(r'[^\w\s]', '', text)
    # Convert to lowercase
    # text = text.lower()

    # Remove consecutive spaces
    text = re.sub(r"\s+", " ", text)
    text = re.sub(r"\.{4,}", " ", text)
    text = re.sub(r"(\w+)(-\s+)(\w+)", replace_fn, text)

    text = re.sub(r"\s{3,}", " ", text)
    text = re.sub(r"-{3,}", "-", text)
    text = re.sub(r"_{3,}", "-", text)
    return text


def pil_image_to_data_url(img):
    im_file = BytesIO()
    img.save(im_file, format="PNG")
    im_bytes = im_file.getvalue()  # im_bytes: image in binary format.
    im_b64 = base64.b64encode(im_bytes).decode("utf-8")
    mime_type, _ = guess_type("file.png")
    return im_b64, mime_type


def local_image_to_data_url(image_path):
    if isinstance(image_path, Image.Image):
        base64_encoded_data, mime_type = pil_image_to_data_url(image_path)
    elif Path(image_path).is_file():
        # Guess the MIME type of the image based n the file extension
        mime_type, _ = guess_type(image_path)
        if mime_type is None:
            mime_type = "application/octet-stream"  # Default MIME type if none is found
        logger.debug(f"Image path: {image_path} exist is {os.path.exists(image_path)}")

        # Read and encode the image file
        with open(image_path, "rb") as image_file:
            base64_encoded_data = base64.b64encode(image_file.read()).decode("utf-8")
    else:
        raise ValueError(f"Not support {type(image_path)} invoke for chat")

    # Construct the data URL
    return f"data:{mime_type};base64,{base64_encoded_data}"


def convert_blob_data_from_docx_to_pdf(blob_data: bytes):
    temp_docx = tempfile.NamedTemporaryFile(
        delete=False, suffix=".docx", prefix="intranet-temp-data-"
    )
    logger.debug(f"Writing blob data to temp file {temp_docx.name}")
    temp_docx.write(blob_data)
    temp_docx.close()

    docx_path = temp_docx.name
    output_dir = os.path.dirname(docx_path)

    command = [
        "soffice",
        "--headless",
        "--convert-to",
        "pdf",
        "--outdir",
        output_dir,
        docx_path,
    ]

    run(command, check=True, stdout=PIPE, stderr=PIPE, timeout=1000)
    pdf_path = docx_path.replace(".docx", ".pdf")  # LibreOffice renames it
    print(f"Converted {docx_path} to PDF: {pdf_path}")
    return docx_path, pdf_path


def convert_blob_data_to_pdf(blob_data: bytes, file_extensions_with_dot: str):
    temp_file = tempfile.NamedTemporaryFile(
        delete=False, suffix=file_extensions_with_dot, prefix="intranet-temp-data-"
    )
    logger.debug(f"Writing blob data to temp file {temp_file.name}")
    temp_file.write(blob_data)
    temp_file.close()

    original_file_path = temp_file.name
    output_dir = os.path.dirname(original_file_path)

    command = [
        "soffice",
        "--headless",
        "--convert-to",
        "pdf",
        "--outdir",
        output_dir,
        original_file_path,
    ]

    run(command, check=True, stdout=PIPE, stderr=PIPE, timeout=1000)
    pdf_path = original_file_path.replace(
        file_extensions_with_dot, ".pdf"
    )  # LibreOffice renames it
    print(f"Converted {original_file_path} to PDF: {pdf_path}")
    return original_file_path, pdf_path


def is_slide_like_pdf_pages(pdf_data) -> bool:
    pdf_documents = fitz.open(stream=BytesIO(pdf_data))
    # Only check the first page (update later for pdf files containing different page ratio)
    first_page = pdf_documents[0]
    width, height = first_page.rect.width, first_page.rect.height
    pdf_documents.close()
    if width > height:
        return True
    else:
        return False


def get_page_rotation(pdf_data) -> bool:
    pdf_documents = fitz.open(stream=pdf_data, filetype="pdf")
    page_rotation = [page.rotation for page in pdf_documents]
    pdf_documents.close()
    return page_rotation


def normalize_number(value: str) -> Union[int, float, None]:
    """
    Normalize a localized numeric string into a float or int.

    Handles:
    - European: '1.000.000,22' => 1000000.22
    - US: '1,000,000.22' => 1000000.22
    - Space-separated: '1 000 000' => 1000000
    - Accounting format: '(3,437)' => -3437
    - Ignores >2 digit decimals, keeps integer part
    - Converts .0 floats to int

    Args:
        value (str): Input string with localized number.

    Returns:
        int | float | None
    """
    value = value.strip().replace(" ", "")

    if not re.search(r"\d", value):
        return None

    is_negative = False

    if value.startswith("(") and value.endswith(")"):
        is_negative = True
        value = value[1:-1]  # remove parentheses

    # European format
    if re.fullmatch(r"\d{1,3}(\.\d{3})*(,\d{1,2})?", value):
        value = value.replace(".", "").replace(",", ".")

    # US format
    elif re.fullmatch(r"\d{1,3}(,\d{3})*(\.\d{1,2})?", value):
        value = value.replace(",", "")

    # Plain with comma decimal
    elif re.fullmatch(r"\d+(,\d{1,2})", value):
        value = value.replace(",", ".")

    # Too long decimal (e.g. 1000000.3333) → discard decimal
    elif re.fullmatch(r"\d+(\.\d{3,})", value):
        value = value.split(".")[0]

    elif re.fullmatch(r"\d+(,\d{3,})", value):
        parts = value.split(",")
        if len(parts[1]) > 2:
            value = parts[0]
        else:
            value = value.replace(",", ".")

    try:
        number = float(value)
        if is_negative:
            number = -number
        return int(number) if number.is_integer() else number
    except ValueError:
        return None
