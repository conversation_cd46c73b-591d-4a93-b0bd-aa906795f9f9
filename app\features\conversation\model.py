from datetime import datetime
from enum import StrEnum

from pydantic import BaseModel
from sqlalchemy import <PERSON>olean, Column, Float, Foreign<PERSON>ey, Integer, String
from sqlalchemy.orm import relationship

from app.database.db import Base
from app.database.model import TimeStampMixin


class Status(StrEnum):
    """
    Enum for status
    """

    INIT = "INITIALIZE"
    PROCESS = "PROCESSING"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    FAIL_TO_UPLOAD = "FAIL_TO_UPLOAD"
    FAIL_TO_CREATE_CONVERSATION = "FAIL_TO_CREATE_CONVERSATION"
    INVALID = "INVALID"


class Operator(StrEnum):
    """
    Enum for operator
    """

    MULTIPLY = "MULTIPLY"
    DIVIDE = "DIVIDE"


# Tables for storing PDF, EXCEL file metadata, and the conversation containing mapping results of the two files
class FinancialDocument(Base, TimeStampMixin):
    __tablename__ = "FinancialDocument"

    id = Column(Integer, autoincrement=True, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    filename = Column(String, nullable=False)  # financial PDF filename
    blob_path = Column(String, nullable=True)  # store the path on Blob Storage
    pdf_metadata = Column(
        String, nullable=True
    )  # Metadata of the file: table markdown, bounding box, etc.
    deleted = Column(Boolean, default=False)
    conversations = relationship("FinancialConversation", back_populates="document")


class FinancialReport(Base, TimeStampMixin):
    __tablename__ = "FinancialReport"

    id = Column(Integer, autoincrement=True, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False)
    document_id = Column(Integer, nullable=False)
    filename = Column(String, nullable=False)  # financial EXCEL filename
    blob_path = Column(String, nullable=True)  # store the path on Blob Storage
    deleted = Column(Boolean, default=False)


class FinancialConversation(Base, TimeStampMixin):
    __tablename__ = "FinancialConversation"

    id = Column(Integer, autoincrement=True, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    conversation_name = Column(String, nullable=False)
    conversation_type = Column(String, nullable=True, default="bp-financial-chat")
    document_id = Column(Integer, ForeignKey("FinancialDocument.id"), nullable=True)
    report_id = Column(Integer, nullable=True)
    mapping_metadata = Column(
        String, nullable=True
    )  # Store mapping data from pdf to excel including highlight, bounding box, etc.
    status = Column(String, default=Status.INIT.value)  # Status of data mapping
    deleted = Column(Boolean, default=False)
    conversion_factor = Column(Float, nullable=True, default=1.0)
    operator = Column(String, nullable=True, default=Operator.MULTIPLY.value)
    document = relationship("FinancialDocument", back_populates="conversations")


class UploadedFile(BaseModel):
    content: bytes = None
    file_name: str = None


# BaseModel for API requests/responses (DTO)
class ConversationCreate(BaseModel):
    user_id: int | None = None
    conversation_name: str = "New Chat"
    # document_name: str | None = None
    # status: str | None = None


class ConversationResponse(BaseModel):
    id: int | None = None
    conversation_name: str | None = None
    document_name: str | None = None
    document_blob_name: str | None = None
    document_download_url: str | None = None
    report_name: str | None = None
    report_blob_name: str | None = None
    report_download_url: str | None = None
    mapping_metadata: str | None = None
    download_url_expired: datetime | None = None
    status: str | None = None


class ConversationListResponse(BaseModel):
    id: int | None = None
    conversation_name: str | None = None
    createdTime: datetime | None = None
    updatedTime: datetime | None = None
    status: str | None = None
    deleted: bool | None = None


class ConversationUpdate(BaseModel):
    conversation_name: str | None = None
    conversion_factor: float | None = None
    mapping_metadata: str | None = None
    operator: str | None = None


class ConversationDelete(BaseModel):
    deleted: bool | None = None


class ItemUpdate(BaseModel):
    sheet_id: int
    screen_id: int
    item_no: int
    value: float


class ConversationItemUpdate(BaseModel):
    items: list[ItemUpdate] = []

    class Config:
        arbitrary_types_allowed = True


class ConversationInfo(BaseModel):
    conversation_id: int
    user_id: int | None = None
    conversation_name: str
    conversation_type: str = "bp-financial-chat"
    document_id: int | None = None
    document_name: str | None = None
    report_id: int | None = None
    mapping_metadata: list[dict] = []
    status: str
    createdTime: datetime | None = None
    conversion_factor: float | None = None
    operator: str | None = None
