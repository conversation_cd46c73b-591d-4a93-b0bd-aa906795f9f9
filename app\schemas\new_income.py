from pydantic import BaseModel, Field

from .base import NewItem


class OperatingMetrics(BaseModel):
    """This class defines core operating metrics that should be extracted from financial report.
    The core operating metrics include:
        - Gross Sales (gross_sales): Total operating revenue after accounting for adjustments such as inventory changes, reversals, and other applicable deductions.
        - Cost of Sales (cost_of_sales): Direct costs attributable to the production of goods/merchandises sold or services provided.
        - Gross Profit (gross_profit_extracted): Difference between revenue and cost of sales.
        - Operating Expenses (operating_expenses): Total operating expenses of the company in the financial statement, which includes selling, general and administrative expenses, etc.
        - Operating Income (operating_income_extracted): Profit from core operations after deducting operating expenses.

    Note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    """

    gross_sales: NewItem = Field(
        description="Total operating revenue after accounting for adjustments such as inventory changes, reversals, and other applicable deductions. (positive value)"
    )
    cost_of_sales: NewItem = Field(
        description="The cost of goods sold (COGS) or direct costs attributable to the production of goods/merchandises sold or services provided. (positive value)"
    )
    gross_profit_extracted: NewItem = Field(
        description="The gross profit or the difference between gross sales and cost of sales. (positive if profit, negative if loss). Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )
    operating_expenses: NewItem | None = Field(
        description="Total operating expenses of the company in the financial statement, which includes selling, general and administrative expenses, etc. (positive value)"
    )
    operating_income_extracted: NewItem = Field(
        description="The operating income or the difference between gross profit and operating expenses. (positive if income, negative if loss). Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )


class NonRecurringIncome(BaseModel):
    """A class representing non-recurring income in a financial statement.

    Attributes:
        Finance income (finance_income): Income earned from interest-bearing assets.
        Dividend Received (dividend_received): Income received from investments in other companies.
        Equity-Method Affiliate Profits (equity_method_affiliate_profits): Profits from investments in affiliated companies.
        Foreign Exchange Gains (foreign_exchange_gains): Profits from changes in currency exchange rates.
        Gain on Sales of Non-Current Assets (gain_on_sales_non_current_assets): Profits from selling or disposing property, equipment, or other non-current assets.
        Gain on Sales of Investment & Securities (gain_on_sales_investment_securities): Profits from selling investment securities.
        Gain from Asset Revaluation (gain_from_asset_revaluation): Gains from adjusting asset values to reflect current market value.
        Reversal of Other Allowance (reversal_of_other_allowance): Income from reversing previously established allowances or provisions.
        Other non-recurring income (other_non_recurring_income): Other non-recurring income items not classified above.
        Total non-recurring income value (total_non_recurring_income_extracted): The sum of all non-recurring income items.

    Note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    """

    finance_income: NewItem | None = Field(
        description="Income earned from interest-bearing assets."
    )
    dividend_received: NewItem | None = Field(
        description="Income received from investments in other companies."
    )
    equity_method_affiliate_profits: NewItem | None = Field(
        description="Profits from investments in affiliated companies."
    )
    foreign_exchange_gains: NewItem | None = Field(
        description="Profits from changes in currency exchange rates."
    )
    gain_on_sales_non_current_assets: NewItem | None = Field(
        description="Profits from selling or disposing property, equipment, or other non-current assets."
    )
    gain_on_sales_investment_securities: NewItem | None = Field(
        description="Profits from selling investment securities."
    )
    gain_from_asset_revaluation: NewItem | None = Field(
        description="Gains from adjusting asset values to reflect current market value."
    )
    reversal_of_other_allowance: NewItem | None = Field(
        description="Income from reversing previously established allowances or provisions."
    )
    other_non_recurring_income: NewItem | None = Field(
        description="Other non-recurring income items not classified above or not included in the pre-defined items, including 'Other income' if explicitly stated."
    )
    total_non_recurring_income_extracted: NewItem | None = Field(
        description="The sum of all non-recurring income items. Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )


class NonRecurringExpenses(BaseModel):
    """A class representing non-recurring expenses in a financial statement.

    Attributes:
        Finance Expenses (finance_expenses): Costs of borrowing money from creditors.
        Equity-Method Affiliate Losses (equity_method_affiliate_losses): Losses from investments in affiliated companies.
        Foreign Exchange Losses (foreign_exchange_losses): Losses from changes in currency exchange rates.
        Loss on Sales & Disposal of Non-Current Assets (loss_on_sales_disposal_non_current_assets): Losses from selling or disposing property, equipment, or other non-current assets.
        Loss on Sales of Investment & Securities (loss_on_sales_investment_securities): Losses from selling investment securities.
        Loss from Asset Revaluation (loss_from_asset_revaluation): Losses from revaluing assets to fair market value.
        Other Provision (other_provision): Estimated future expenses recorded in the current period.
        Other Non-Recurring Loss (other_non_recurring_loss): Other non-recurring loss items not classified above or not included in the pre-defined items.
        (total_non_recurring_expenses_extracted): Sum of all non-operating and extraordinary expenses.

    Note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    """

    finance_expenses: NewItem | None = Field(
        description="Costs of borrowing money from creditors."
    )
    equity_method_affiliate_losses: NewItem | None = Field(
        description="Losses from investments in affiliated companies."
    )
    foreign_exchange_losses: NewItem | None = Field(
        description="Losses from changes in currency exchange rates."
    )
    loss_on_sales_disposal_non_current_assets: NewItem | None = Field(
        description="Losses from selling or disposing property, equipment, or other non-current assets."
    )
    loss_on_sales_investment_securities: NewItem | None = Field(
        description="Losses from selling investment securities."
    )
    loss_from_asset_revaluation: NewItem | None = Field(
        description="Losses from adjusting asset values to reflect current market value."
    )
    other_provision: NewItem | None = Field(
        description="Estimated future expenses recorded in the current period."
    )
    other_non_recurring_loss: NewItem | None = Field(
        description="Other non-recurring loss items not classified above or not included in the pre-defined items, including 'Other expenses' if explicitly stated."
    )
    total_non_recurring_expenses_extracted: NewItem | None = Field(
        description="Sum of all non-operating and extraordinary expenses. Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )


class IncomeStatement(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to net income, which includes:
    - Core operating metrics (operating_metrics): Core business revenue, costs, and operating profit metrics.
    - Non-Recurring Income (non_recurring_income): Income from non-regular business activities.
    - Non-Recurring Expenses (non_recurring_expenses): Expenses from non-regular business activities.
    - Non-Recurring Items before Tax (non_recurring_items_before_tax): Non-recurring items (or Extraordinary Items) before tax effects are applied, often used to adjust income for one-time events.
    - Net income before tax (income_before_taxes_extracted): Total profit before accounting for income taxes.
    - Income Taxes (income_taxes): adjusted current taxes which include current period income tax and adjustments for prior year tax expenses or credits, including short or excess provisions
    - Income Taxes Deferred (income_taxes_deferred): Future tax obligations or benefits recognized in the current period.
    - Net Income before Minority Interests (income_before_minority_interests_extracted): Income after taxes but before deducting minority interests.
    - Minority Interests (minority_interests): Portion of subsidiary income belonging to minority shareholders.
    - Net Profit from Discontinued Operations (net_profit_discontinued_operations): Profit from operations that have been terminated.
    - Non-recurring items after tax (non_recurring_items_after_tax): Non-recurring items (or Extraordinary Items) after tax effects are applied, often used to assess the company's core profitability.
    - Net Income Attributable to Owners of the Parent (net_income_owners_extracted): Final profit attributable to the parent company’s shareholders after all expenses, taxes, and minority interests.

    Note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    """

    operating_metrics: OperatingMetrics | None = Field(
        description="Core business revenue, costs, and operating profit metrics."
    )
    non_recurring_income: NonRecurringIncome | None = Field(
        description="Income not related to core business operations."
    )
    non_recurring_expenses: NonRecurringExpenses | None = Field(
        description="Expenses not related to core business operations."
    )
    non_recurring_items_before_tax: NewItem | None = Field(
        description="Non-recurring items (or Extraordinary Items) before tax effects are applied, often used to adjust income for one-time events."
    )
    income_before_taxes_extracted: NewItem | None = Field(
        description="Total profit before accounting for tax obligations. Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )
    income_taxes: NewItem | None = Field(
        description="Current period's income tax and adjustments such as tax expenses or credits relating to prior years and any short/excess tax provisions."
    )
    income_taxes_deferred: NewItem | None = Field(
        description="Future tax obligations or benefits recognized in the current period."
    )
    income_before_minority_interests_extracted: NewItem | None = Field(
        description="Income after taxes but before minority interests. Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )
    minority_interests: NewItem | None = Field(
        description="Portion of subsidiary income belonging to minority shareholders."
    )
    net_profit_discontinued_operations: NewItem | None = Field(
        description="Profit from operations that have been terminated."
    )
    non_recurring_items_after_tax: NewItem | None = Field(
        description="Non-recurring items (or Extraordinary Items) after tax effects are applied, often used to assess the company's core profitability."
    )
    net_income_owners_extracted: NewItem | None = Field(
        description="Net income attributable to owners of the parent company after all expenses, taxes, and minority interests. Only extract the value directly as presented if there is any. Set the value to None otherwise."
    )
