"""Remove not null constraint

Revision ID: 6d7e2f4d865b
Revises: f64670adb724
Create Date: 2025-04-09 11:32:10.840438

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6d7e2f4d865b"
down_revision = "f64670adb724"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "FinancialConversation",
        "mapping_metadata",
        existing_type=sa.VARCHAR(),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "FinancialConversation",
        "mapping_metadata",
        existing_type=sa.VARCHAR(),
        nullable=False,
    )
    # ### end Alembic commands ###
