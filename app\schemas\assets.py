from pydantic import BaseModel, Field

from app.utils.helper import print_value

from .base import Item


class CurrentAssets(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to current assets, which includes:
    - Cash & Bank Deposits (cash_bank_deposits): Money held in bank accounts and other liquid cash equivalents.
    - Trade Accounts Receivable (trade_accounts_receivable): Amounts owed by customers for goods or services delivered on credit.
    - Trade Receivables from Related Companies (trade_receivables_from_related_companies): Amounts owed by affiliated or parent companies for transactions on credit.
    - Lease Receivable & Investment Assets (lease_receivable_investment_assets): Current portion of lease receivables and short-term investments.
    - Securities (securities): Marketable financial instruments that can be easily converted to cash.
    - Inventories (inventories): Raw materials, work-in-progress, and finished goods held for sale.
    - Advances to Suppliers (advances_to_suppliers): Prepayments made to suppliers for future goods or services.
    - Prepaid Expense (prepaid_expense): Expenses paid in advance for future periods.
    - Other Accounts Receivable & Accrued Income (other_accounts_receivable_accrued_income): Miscellaneous receivables and income earned but not yet received.
    - Short-term Loans Receivable (short_term_loans_receivable): Loans to be repaid within 12 months.
    - ST Loans Receivable from Related Co (st_loans_receivable_from_related_co): Short-term loans to affiliated or parent companies.
    - Deferred Tax Assets (deferred_tax_assets): Future tax benefits arising from temporary differences in financial and tax reporting.
    - Other Current Assets (other_current_assets): Miscellaneous short-term assets not classified under specific categories.
    - Allowance for Doubtful Accounts (allowance_for_doubtful_accounts): Reserve for potentially uncollectible receivables.
    and TTL Current Assets (ttl_current_assets).

    Please note that these values should be extracted from the balance sheet in financial report and be the value of the end of the year.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    In case there are fields that not belong to pre-defined items, you can add it to 'Other Current Assets'.
    """

    cash_bank_deposits: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Cash & Bank Deposits', default as None",
    )
    trade_accounts_receivable: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Trade Accounts Receivable', default as None",
    )
    trade_receivables_from_related_companies: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Trade Receivables from Related Companies', default as None",
    )
    lease_receivable_investment_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Lease Receivable & Investment Assets', default as None",
    )
    securities: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Securities', default as None",
    )
    inventories: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Inventories', default as None",
    )
    advances_to_suppliers: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Advances to Suppliers', default as None",
    )
    prepaid_expense: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Prepaid Expense', default as None",
    )
    other_accounts_receivable_accrued_income: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Accounts Receivable & Accrued Income', default as None",
    )
    short_term_loans_receivable: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Short-term Loans Receivable', default as None",
    )
    st_loans_receivable_from_related_co: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'ST Loans Receivable from Related Co', default as None",
    )
    deferred_tax_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Deferred Tax Assets', default as None",
    )
    other_current_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Current Assets', default as None",
    )
    allowance_for_doubtful_accounts: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Allowance for Doubtful Accounts', default as None",
    )
    ttl_current_assets: Item = Field(
        description="The total values of current assets in the financial statement",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the current assets values.
        """
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class TangibleFixedAssets(BaseModel):
    """
    This class defines values that should be extracted from balance sheet of the report.
    All data should be the value at the end of the year.
    These values belong to tangible fixed assets, which includes:
    - Buildings & Structures (buildings_structures): Physical structures owned by the company.
    - TTL Machinery & Fixtures (ttl_machinery_fixtures): Total machinery, equipment, and fixtures used in operations.
    - Lease Assets (lease_assets): Assets acquired through lease agreements.
    - Accumulated Depreciation (accumulated_depreciation): Total depreciation charged against tangible assets, will not be included in the sum.
    - Land (land): Real estate owned by the company.
    - Construction in Progress (construction_in_progress): Costs incurred for assets under construction.
    - Other Tangible Fixed Assets (other_tangible_fixed_assets): Miscellaneous tangible assets not classified under specific categories.
    and TTL Tangible Fixed Assets (ttl_tangible_fixed_assets) be the total tangible fixed assets from financial statement.

    Please note that these values should be extracted from the balance sheet in financial report and be the value of the end of the year.
    In case one item is not available (cannot find in FS), please set the value to None.
    In case there are fields that not belong to pre-defined items, you can add it to 'Other Tangible Fixed Assets'.
    """

    buildings_structures: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Buildings & Structures', default as None",
    )
    ttl_machinery_fixtures: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'TTL Machinery & Fixtures', default as None",
    )
    lease_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Lease Assets', default as None",
    )
    accumulated_depreciation: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Accumulated Depreciation', default as None",
    )
    land: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Land', default as None",
    )
    construction_in_progress: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Construction in Progress', default as None",
    )
    other_tangible_fixed_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Tangible Fixed Assets', default as None",
    )
    ttl_tangible_fixed_assets: Item | None = Field(
        description="The value of the item in the financial statement that map to the total tangible fixed assets",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the tangible fixed assets values.
        """
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class IntangibleAssets(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to intangible assets, which includes:
    - Goodwill (goodwill): Excess of purchase price over fair value of net assets in business combinations.
    - Other Intangible Fixed Assets (other_intangible_fixed_assets): Other non-physical assets such as patents, trademarks, and software.
    and TTL Intangible Assets (ttl_intangible_assets) be the total in-tangible assets from financial statement.

    Please note that these values should be extracted from the balance sheet in financial report and be the value of the end of the year.
    In case one item is not available (cannot find in FS), please set the value to None.
    In case there are fields in FS that not belong to pre-defined items, you can add it to 'Other Intangible Fixed Assets'.
    """

    goodwill: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Goodwill', default as None",
    )
    other_intangible_fixed_assets: Item | None = Field(
        description="The value of the item in the financial statement that can map to 'Other Intangible Fixed Assets', default as None",
    )
    ttl_intangible_assets: Item | None = Field(
        description="The value of the item in the financial statement that map to the total in-tangible assets",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the intangible fixed assets values.
        """
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class InvestmentAssets(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to investment and other assets (in balance sheet), which includes:
    - Investment Securities & Investments in Capital (sercurities_n_capital_investments): Investment securities and investments in capital.
    - Capital Investment of Subsidiaries & Affiliates (capital_investment_of_subsidiaries_affiliates): Capital investments in subsidiaries and affiliates.
    - Long-term Loans Receivable (long_term_loans_receivable): Loans receivable with a maturity of more than one year.
    - LT Loans & Accounts Receivable from Related Co (lt_loans_accounts_receivable_from_related_co): Long-term loans and accounts receivable from related companies.
    - Doubtful Receivables (doubtful_receivables): Receivables that are expected to be uncollectible.
    - Long-term Prepaid Expenses (long_term_prepaid_expenses): Prepaid expenses with a maturity of more than one year.
    - Real Estate for Investment (real_estate_for_investment): Real estate held for investment purposes.
    - Lease & Guarantee Deposits (lease_n_guarantee_deposits): Deposits made for leases and guarantees.
    - Deferred Tax Assets (deferred_tax_assets): Future tax benefits arising from temporary differences in financial and tax reporting.
    - Allowance for Doubtful Accounts (allowance_for_doubtful_accounts): Reserve for potentially uncollectible receivables.
    - Other Investment & LT Assets (other_investment_and_lt_assets): Miscellaneous long-term investment and assets not classified under specific categories.
    and TTL Investment & Other Assets (ttl_investment_and_other_assets) be the total investment and other assets from financial statement.

    Please note that these values should be extracted from the balance sheet in financial report and be the value of the end of the year.
    """

    sercurities_n_capital_investments: Item | None = Field(
        description="The fixed assets data about investment securities and investments in capital"
    )
    capital_investment_of_subsidiaries_affiliates: Item | None = Field(
        description="The fixed assets data about capital investment of subsidiaries and affiliates"
    )
    long_term_loans_receivable: Item | None = Field(
        description="The assets data about long term loans receivable"
    )
    lt_loans_accounts_receivable_from_related_co: Item | None = Field(
        description="The fixed assets data about long term loans and accounts receivable from related companies"
    )
    doubtful_receivables: Item | None = Field(
        description="The fixed assets data about doubtful receivables"
    )
    long_term_prepaid_expenses: Item | None = Field(
        description="The fixed assets data about long term prepaid expenses"
    )
    real_estate_for_investment: Item | None = Field(
        description="The fixed assets data about real estate for investment"
    )
    lease_n_guarantee_deposits: Item | None = Field(
        description="The fixed assets data about lease and guarantee deposits"
    )
    deferred_tax_assets: Item | None = Field(
        description="The fixed assets data about deferred tax assets"
    )
    allowance_for_doubtful_accounts: Item | None = Field(
        description="The fixed assets data about allowance for doubtful accounts"
    )
    other_investment_and_lt_assets: Item | None = Field(
        description="The fixed assets data about other investment and long term assets"
    )
    ttl_investment_and_other_assets: Item | None = Field(
        description="The fixed assets data about total investment and other assets"
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the investment and other assets values.
        """
        data = []
        for key, value in self.__dict__.items():
            if isinstance(value, Item):
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value.value / exchange_rate)}"
                )
            elif value is not None:
                data.append(
                    f"{key.replace('_', ' ').capitalize()}: {print_value(value / exchange_rate)}"
                )
        return "\n".join(data)


class FixedAssets(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    Note these values should be the data at the end of the year.
    These values belong to fixed assets, which includes:
    - Tangible Fixed Assets (tangible_fixed_assets): Physical assets such as buildings, machinery, and equipment.
    - Intangible Assets (intangible_assets): Non-physical assets such as goodwill and intellectual property.
    - Total Fixed Assets (fixed_assets): The total value of all fixed assets.
    """

    tangible_fixed_assets: TangibleFixedAssets = Field(
        description="The total tangible fixed assets of company in financial report, which includes buildings, machinery, and equipment.",
    )
    intangible_assets: IntangibleAssets = Field(
        description="The total intangible assets of company in financial report",
    )
    investment_and_other_assets: InvestmentAssets = Field(
        description="The fixed assets that are investment and other assets",
    )
    fixed_assets: Item = Field(
        description="The total fixed assets of company extracted from the financial statement",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the fixed assets values.
        """
        data = [
            f"Tangible Fixed Assets:\n{self.tangible_fixed_assets.pretty_print(exchange_rate)}",
            f"Intangible Assets:\n{self.intangible_assets.pretty_print(exchange_rate)}",
            f"Investment & Other Assets:\n{self.investment_and_other_assets.pretty_print(exchange_rate)}",
            f"Fixed Assets: {print_value(self.fixed_assets.value / exchange_rate)}",
        ]
        return "\n\n".join(data)


class Assets(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to assets, which includes:
    - Current Assets (current_assets)
    - Fixed Assets (fixed_assets)
    """

    current_assets: CurrentAssets = Field(
        description="The total current assets of the company at the end of the year extracted from financial report",
    )
    fixed_assets: FixedAssets = Field(
        description="The instance containing information about all fixed assets of the company mentioned in the financial report.",
    )
    total_asset: Item = Field(
        description="the value of total assets of the company in financial report at the end of the period",
    )

    def pretty_print(self, exchange_rate: float = 1.0) -> str:
        """
        Pretty print the assets values.
        """
        data = []
        data = [
            f"Current Assets:\n{self.current_assets.pretty_print(exchange_rate)}",
            f"Fixed Assets:\n{self.fixed_assets.pretty_print(exchange_rate)}",
        ]
        data.append(
            f"Total Assets: {print_value(self.total_asset.value / exchange_rate)}"
        )
        return "\n\n".join(data)
