from typing import Literal

import pandas as pd
from langchain_core.messages import AI<PERSON>essage, AnyMessage, HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import ToolNode, create_react_agent
from langgraph.types import Command
from loguru import logger
from pydantic import BaseModel

from app.agents.base import Checkpointer, IncomeGraphState, SubGraphState
from app.agents.highlights.utils import convert_to_dataframe, transform_to_output_item
from app.schemas.income import Income
from app.schemas.new_income import IncomeStatement
from app.tools.base import get_sum_values_income
from app.tools.income import check_income_sum
from app.utils.azure_openai_client import azure_openai_chat_model
from app.utils.constants import MAX_RETRIES, RETRY_DELAY
from app.utils.utils import retry_on_rate_limit

from .prompt import (
    evaluate_human_prompt,
    evaluate_system_prompt,
    human_prompt,
    reflect_human_prompt_1,
    reflect_human_prompt_2,
    reflect_system_prompt_1,
    reflect_system_prompt_2,
    summarize_human_prompt,
    system_prompt,
)


def create_income_agent(
    tools: list = [check_income_sum],
    model: AzureChatOpenAI = azure_openai_chat_model,
    response_format: type[BaseModel] = Income,
    checkpointer: Checkpointer | None = None,
    max_retries: int = 2,
) -> CompiledStateGraph:
    """Create an Income agent graph.

    Args:
        tools: list of tools to be used by the agent, default is check_income_sum.
        model: the LLM to be used for the agent, default is Azure OpenAI Chat model.
        response_format: the pydantic model to be used for structured output,
            default is Income.
        checkpointer: message state persistence checkpoint to be used.
        max_retries: maximum number of times to retry calling tools, default is 2.

    Returns:
        A compiled graph that can be used to execute the Income agent.
    """
    workflow = StateGraph(SubGraphState)

    tool_node = ToolNode(tools, name="tool_node")
    llm_with_tool = model.bind_tools(list(tool_node.tools_by_name.values()))
    llm_with_structured = model.with_structured_output(response_format)

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def call_model(
        state: SubGraphState,
    ) -> Literal["generate_structured_output", "tool_node"]:
        if state["number_of_tool_calls"] >= max_retries:
            logger.debug(
                f"Go to generate_structured_output since #tool_call >= {max_retries}"
            )
            return Command(goto="generate_structured_output")

        messages = state["messages"]
        response: AIMessage = await llm_with_tool.ainvoke(input=messages)

        if response.tool_calls:
            if response.tool_calls[0]["name"] == "check_income_sum":

                logger.debug("go to check_income_sum")
                return Command(
                    goto="tool_node",
                    update={
                        "messages": [response],
                        "number_of_tool_calls": state["number_of_tool_calls"] + 1,
                    },
                )
            logger.debug(f"Use tool: {response.tool_calls[0]['name']}")
            return Command(
                goto="tool_node",
                update={
                    "messages": [response],
                },
            )
        logger.debug("Go to generate_structured_output, finish task")
        return Command(
            goto="generate_structured_output", update={"messages": [response]}
        )

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def generate_structured_output(state: SubGraphState) -> Literal["END"]:
        messages = state["messages"]
        logger.debug("Start generate structured output")
        output = await llm_with_structured.ainvoke(input=messages)
        logger.debug(f"output: {output}")
        return Command(goto="END", update={"structured_response": output})

    workflow.add_node("agent", call_model)
    workflow.add_node("generate_structured_output", generate_structured_output)
    workflow.add_node(tool_node)
    workflow.set_entry_point("agent")
    workflow.add_edge("tool_node", "agent")

    app = workflow.compile(checkpointer=checkpointer)

    return app


def get_income_agent_messages(data: str) -> list[AnyMessage]:
    """Generate default message for asset agent

    Args:
        data (str): the financial data extracted from pdf file

    Returns:
        list[AnyMessage]: list of messages
    """
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=human_prompt.format(data)),
    ]
    return messages


def create_income_agent_v2(
    model: AzureChatOpenAI = azure_openai_chat_model,
) -> CompiledStateGraph:
    """Create an Income agent graph.

    Returns:
        A compiled graph that can be used to execute the Income agent.
    """
    llm_with_structured = model.with_structured_output(
        IncomeStatement,
        include_raw=True,
    )
    evaluate_agent = create_react_agent(
        model=model,
        tools=[get_sum_values_income],
    )

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def generate_response(state: IncomeGraphState):
        """Generate the response from the agent."""
        logger.debug("Income agent generates response")
        messages = state["messages"]
        output: dict = await llm_with_structured.ainvoke(
            messages,
            # config={"callbacks": [langfuse_handler]},
        )

        raw_output = output["raw"]
        # structured_response: IncomeStatement = output["parsed"]
        return {
            "messages": [raw_output],
            "structured_response": output["parsed"],
        }

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def reflect_response(state: IncomeGraphState):
        """Evaluate the response from the agent."""
        structured_response = state.get("structured_response")
        if not structured_response:
            return {}

        # Transform and filter data
        income_items = transform_to_output_item(structured_response)
        income_df = convert_to_dataframe(income_items)

        ids_to_exclude = {
            "gross_profit_extracted",
            "operating_income_extracted",
            "total_non_recurring_income_extracted",
            "total_non_recurring_expenses_extracted",
            "income_before_taxes_extracted",
            "income_before_minority_interests_extracted",
            "net_income_owners_extracted",
        }

        if "id" in income_df.columns:
            filtered_df = income_df[~income_df["id"].isin(ids_to_exclude)]
        else:
            filtered_df = pd.DataFrame({})

        fields = ",".join(filtered_df["id"]) if not filtered_df.empty else ""

        # First reflection step
        messages = [
            SystemMessage(content=reflect_system_prompt_1),
            HumanMessage(
                content=reflect_human_prompt_1.format(
                    table_markdown=state["data"],
                    results=filtered_df.to_csv(),
                )
            ),
        ]
        logger.debug("Start reflecting the response")
        reflection_output = await model.ainvoke(messages)

        # Second reflection step
        messages = [
            SystemMessage(content=reflect_system_prompt_2),
            HumanMessage(
                content=reflect_human_prompt_2.format(
                    content=reflection_output.content,
                    fields=fields,
                )
            ),
        ]
        output = await model.ainvoke(messages)

        # Summarize the reflection output
        logger.debug("Start summarizing the reflection response")
        summary = await model.ainvoke(
            [
                HumanMessage(
                    content=summarize_human_prompt.format(content=output.content)
                )
            ]
        )

        if isinstance(summary, AIMessage):
            return {
                "messages": [HumanMessage(content=summary.content)],
                "is_reflected": True,
            }

        return {}

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def evaluate_response(state: IncomeGraphState):
        """Evaluate the response from the agent."""
        structured_response = state["structured_response"]
        if not structured_response:
            return {}

        income_output_items = transform_to_output_item(structured_response)
        income_df = convert_to_dataframe(income_output_items)

        messages = [
            SystemMessage(content=evaluate_system_prompt),
            HumanMessage(
                content=evaluate_human_prompt.format(
                    table_markdown=state["data"],
                    results=income_df.to_csv(),
                )
            ),
        ]

        logger.debug("Start evaluating the response")
        evaluation_output: dict = await evaluate_agent.ainvoke(
            {"messages": messages},
            # config={"callbacks": [langfuse_handler]},
        )
        if isinstance(evaluation_output["messages"][-1], AIMessage):
            msg_content = evaluation_output["messages"][-1].content

            return {
                "messages": [HumanMessage(content=msg_content)],
                "is_evaluated": True,
            }
        return {}

    # Define the workflow
    workflow = StateGraph(IncomeGraphState)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("reflect_response", reflect_response)
    workflow.add_node("evaluate_response", evaluate_response)
    workflow.add_conditional_edges(
        "generate_response",
        lambda state: (
            END
            if state["is_evaluated"]
            else "evaluate_response" if state["is_reflected"] else "reflect_response"
        ),
        [END, "evaluate_response", "reflect_response"],
    )
    workflow.add_edge("evaluate_response", "generate_response")
    workflow.add_edge("reflect_response", "generate_response")
    workflow.set_entry_point("generate_response")
    app = workflow.compile()

    return app
