import os
from dataclasses import dataclass
from typing import Union

from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from dotenv import dotenv_values, load_dotenv

# Load variables from .env file into a dictionary
if os.path.exists(".env"):
    load_dotenv()
    config = dotenv_values(".env")
else:
    config = {}


INPUT_TOKEN_LIMIT = 50000


def get_secrets_from_key_vaults():
    VAULT_URL = os.getenv("VAULT_URL", "https://itochu-keyvaults.vault.azure.net/")
    # Use DefaultAzureCredential to authenticate using Azure Managed Identity, Azure CLI, or Azure AD credentials
    credential = DefaultAzureCredential(
        connection_verify=False, exclude_shared_token_cache_credential=True
    )
    # Create key and secret clients
    secret_client = SecretClient(vault_url=VAULT_URL, credential=credential)

    # Initialize an empty dictionary to store secrets
    config = {}

    # Retrieve secrets
    for secret in secret_client.list_properties_of_secrets():
        secret_value = secret_client.get_secret(secret.name).value
        config[secret.name] = secret_value

    return config


if not config:
    # Get secrets key from Key vaults
    config = get_secrets_from_key_vaults()


@dataclass
class AzureOpenAI:
    azure_endpoint: str = config.get("ANALYSIS-AZURE-OPENAI-ENDPOINT")
    api_key: str = config.get("ANALYSIS-AZURE-OPENAI-API-KEY")
    api_version: str = config.get("ANALYSIS-AZURE-OPENAI-API-VERSION")
    azure_deployment: str = config.get("ANALYSIS-AZURE-OPENAI-DEPLOYMENT")
    model_name: str = config.get("ANALYSIS-AZURE-OPENAI-MODEL-NAME")


@dataclass
class FastAzureOpenAI:
    azure_endpoint: str = config.get("ANALYSIS-FAST-AZURE-OPENAI-ENDPOINT")
    api_key: str = config.get("ANALYSIS-FAST-AZURE-OPENAI-API-KEY")
    api_version: str = config.get("ANALYSIS-FAST-AZURE-OPENAI-API-VERSION")
    azure_deployment: str = config.get("ANALYSIS-FAST-AZURE-OPENAI-DEPLOYMENT")
    model_name: str = config.get("ANALYSIS-FAST-AZURE-OPENAI-MODEL-NAME")


@dataclass
class AzureOpenAIEmbeddingConfig:
    azure_endpoint: str = config.get("ANALYSIS-AZURE-OPENAI-EMBEDDING-ENDPOINT")
    api_key: str = config.get("ANALYSIS-AZURE-OPENAI-EMBEDDING-API-KEY")
    api_version: str = config.get("ANALYSIS-AZURE-OPENAI-EMBEDDING-API-VERSION")
    openai_api_type: str = "azure"
    azure_deployment: str = config.get(
        "ANALYSIS-AZURE-OPENAI-EMBEDDING-DEPLOYMENT-NAME"
    )
    model: str = config.get("ANALYSIS-AZURE-OPENAI-EMBEDDING-MODEL-NAME")
    chunk_size: int = config.get("ANALYSIS-AZURE-OPENAI-EMBEDDING-CHUNK-SIZE", 128)
    embedding_vector_dimension: int = config.get(
        "ANALYSIS-AZURE-OPENAI-EMBEDDING-VECTOR-DIMENSION", 1536
    )


@dataclass
class DocIntelConfig:
    doc_intel_endpoint = config.get("ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-ENDPOINT")
    doc_intel_key = config.get("ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-KEY")
    doc_intel_model = config.get("ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-MODEL")


@dataclass
class AzureBlobConfig:
    blob_connection_str: str = config.get("ANALYSIS-STORAGE-CONNECTION-STRING")
    container_name: str = config.get("ANALYSIS-BLOB-CONTAINER")


@dataclass
class Database:
    postgres_db_host: str = config.get("ANALYSIS-DB-HOST", "")
    postgres_db_port: str = config.get("ANALYSIS-DB-PORT", 5432)
    postgres_db_user: str = config.get("ANALYSIS-DB-USER", "")
    postgres_db_password: str = config.get("ANALYSIS-DB-PASSWORD", "")

    # Main database
    postgres_db_name: str = config.get("ANALYSIS-DB-NAME", "")
    postgres_db_url: str = (
        f"postgresql://{postgres_db_user}:{postgres_db_password}@{postgres_db_host}:{postgres_db_port}/{postgres_db_name}"
    )

    # checkpoint database
    postgres_checkpoint_url: str = (
        f"postgresql://{postgres_db_user}:{postgres_db_password}@{postgres_db_host}:{postgres_db_port}/{postgres_db_name}"
    )
    pool_size: int = config.get("ANALYSIS-DB-POOL-SIZE", 100)
    max_overflow: int = config.get("ANALYSIS-DB-MAX-OVERFLOW", 0)


@dataclass
class Folder:
    SAVE_DIR = os.path.join(os.getcwd(), "local_files")
    os.makedirs(SAVE_DIR, exist_ok=True)


@dataclass
class LangfuseCallback:
    is_local: bool = False
    public_key: str = config.get("ANALYSIS-LANGFUSE-PUBLIC-KEY")
    secret_key: str = config.get("ANALYSIS-LANGFUSE-SECRET-KEY")
    host: str = config.get("ANALYSIS-LANGFUSE-HOST")


@dataclass
class JWT:
    jwt_secret_key: str = config.get("ANALYSIS-JWT-SECRET-KEY")
    jwt_alg: str = config.get("ANALYSIS-JWT-ALG")
    access_token_expire_minutes: int = 60 * 5  # 5 hours
    refresh_token_expire_minutes: int = 60 * 24 * 60  # 60 days


class LogAnalytics:
    workspace_id: Union[str, None]
    shared_key: Union[str, None]
    custom_table: Union[str, None]

    def __init__(self) -> None:
        self.workspace_id = config.get("WORKSPACE-ID-LOG", "workspace ID")
        self.shared_key = config.get("PRIMARY-KEY-LOG", "")
        self.custom_table = config.get("CUSTOM-TABLE-LOG", "CustomLogAPI")
