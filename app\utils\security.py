import hashlib
from time import time

import jwt
from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.settings import JWT

access_token_jwt_subject = "access_token"


def verify_password(plain_password, hashed_password):
    return hashlib.md5(plain_password.encode("utf8")).hexdigest() == hashed_password


def get_password_hash(password):
    return hashlib.md5(password.encode("utf8")).hexdigest()


def create_refresh_token(uid, email):
    rt = int(time())
    expire = int(JWT().refresh_token_expire_minutes) * 60
    token = jwt.encode(
        {
            "rt": rt,
            "expire_after": expire,
            "uid": uid,
            "email": email,
            "tok_type": "refresh",
        },
        str(JWT.jwt_secret_key),
        algorithm=JWT.jwt_alg,
    )
    return token


def create_access_token(*, data: dict):
    rt = int(time())
    to_encode = data.copy()
    expire = int(JWT().access_token_expire_minutes) * 60
    to_encode.update(
        {"rt": rt, "expire_after": expire, "sub": access_token_jwt_subject}
    )
    encoded_jwt = jwt.encode(to_encode, str(JWT.jwt_secret_key), algorithm=JWT.jwt_alg)
    return encoded_jwt


def validate_token(token):
    # Returns Is_Valid, user_id
    rt = int(time())
    token_raw = None
    try:
        token_raw = jwt.decode(token, JWT.jwt_secret_key, algorithms=JWT.jwt_alg)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    tk_rt = token_raw["rt"]
    tk_ex = token_raw["expire_after"]

    # validate time expired token
    if tk_rt <= rt and (rt - tk_rt) <= int(tk_ex):
        return True, token_raw
    else:
        # token is outdated
        return False, None


def decode_token_azure(token):
    signature = {"verify_signature": False}
    payload = jwt.decode(token, options=signature, algorithms=[JWT.jwt_alg])
    return payload
