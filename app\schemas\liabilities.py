from pydantic import BaseModel, Field

from app.utils.helper import print_value

from .base import Item


class CurrentLiabilities(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to current liabilities, which includes:
    - Trade Notes Payable (trade_notes_payable): Short-term promissory notes issued for purchases, usually with fixed payment terms.
    - Trade Accounts Payable (trade_accounts_payable): Amounts the company owes to external suppliers for goods or services purchased on credit in the normal course of business.
    - Trade Payables to Related Companies (trade_payables_to_related_companies): Amounts the company owes to affiliated or related companies (such as subsidiaries or parent firms) for goods or services purchased on credit.
    - Short-term Loans Payable (short_term_loans_payable): Loans due within 12 months, typically from banks or financial institutions.
    - Short-term Loans Payable to Related Companies (short_term_loans_payable_to_related_companies): Short-term borrowings from affiliated or parent companies.
    - Current Portion of Long-term Borrowings (current_portion_of_long_term_borrowings): The portion of long-term debt that must be repaid within the next 12 months.
    - Lease Obligations (lease_obligations): The current year's portion of lease payments under operating or finance leases.
    - A/P & Accrued Expense (a_p_accrued_expense): Unpaid operating expenses and obligations incurred but not yet settled.
    - Income Tax Payable (income_tax_payable): Taxes owed to the government for the current period but not yet paid.
    - Dividends Payable (dividends_payable): Declared dividends that are owed to shareholders but not yet distributed.
    - A/R & Unearned Revenue (a_r_unearned_revenue): Advance payments received from customers for goods or services not yet delivered.
    - Other Allowances (other_allowances): Other Allowances are estimated short-term liabilities set aside for expected future costs such as warranties, returns, bonuses, or restructuring, where the amount or timing is not yet certain but the obligation is probable
    - Deferred Tax Liabilities (deferred_tax_liabilities): Temporary tax obligations arising from differences in financial and tax reporting, payable within one year.
    - Other Current Liabilities (other_current_liabilities): Miscellaneous short-term obligations not classified under specific categories above.
    and current liabilities (current_liabilities).

    Please note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    """

    trade_notes_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Trade Notes Payable' in ITAM-G.",
    )
    trade_accounts_payable: Item | None = Field(
        None,
        description="Amounts the company owes to external suppliers for goods or services purchased on credit in the normal course of business.",
    )
    trade_payables_to_related_companies: Item | None = Field(
        None,
        description="Amounts the company owes to affiliated or related companies for goods or services purchased on credit.",
    )
    short_term_loans_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Short-term Loans Payable' in ITAM-G.",
    )
    short_term_loans_payable_to_related_companies: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Short-term Loans Payable to Related Companies' in ITAM-G.",
    )
    current_portion_of_long_term_borrowings: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Current Portion of Long-term Borrowings' in ITAM-G.",
    )
    lease_obligations: Item | None = Field(
        None,
        description="The current year's portion of lease payments under operating or finance leases",
    )
    a_p_accrued_expense: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'A/P & Accrued Expense' in ITAM-G.",
    )
    income_tax_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Income Tax Payable' in ITAM-G.",
    )
    dividends_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Dividends Payable' in ITAM-G.",
    )
    a_r_unearned_revenue: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'A/R & Unearned Revenue' in ITAM-G.",
    )
    other_allowances: Item | None = Field(
        None,
        description="Other Allowances are estimated short-term liabilities set aside for expected future costs such as warranties, returns, bonuses, or restructuring, where the amount or timing is not yet certain but the obligation is probable",
    )
    deferred_tax_liabilities: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Deferred Tax Liabilities' in ITAM-G.",
    )
    other_current_liabilities: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Other Current Liabilities' in ITAM-G.",
    )
    current_liabilities: Item | None = Field(
        None,
        description="The total current liabilities, this field should equal to total of all other values.",
    )

    # def __str__(self) -> str:
    #     values = []
    #     for key, value in self.__dict__.items():
    #         if value is not None:
    #             if isinstance(value, Item):
    #                 values.append(
    #                     f"{key.replace('_', ' ').capitalize()}: {value.value} - note: {value.note}"
    #                 )
    #             else:
    #                 values.append(f"{key.replace('_', ' ').capitalize()}: {value}")
    #     return "\n".join(values)

    def pretty_print(self, exchange_rate: float = 1) -> None:
        values = []
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Item):
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value.value/exchange_rate)} - note: {value.note}"
                    )
                else:
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value/exchange_rate)}"
                    )
        return "\n".join(values)


class LongTermLiabilities(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to long-term liabilities, which includes:
    - Bonds & Debentures (bonds_debentures): Debt securities issued by the company to raise long-term capital, typically with fixed interest payments.
    - Long-term Loans Payable (long_term_loans_payable): Loans that the company must repay after more than 12 months, often from banks or financial institutions.
    - Long-term Loans Payable to Related Companies (long_term_loans_payable_to_related_companies): Loans owed to affiliated or parent companies, repayable over a long-term period.
    - Lease Obligations (lease_obligations): Liabilities arising from leasing assets, including finance leases requiring long-term payments.
    - Long-term Other Accounts Payable (long_term_other_accounts_payable): Outstanding liabilities that do not fall under typical loans or leases, due after one year.
    - Long-term Deposits (long_term_deposits): Security deposits or guarantees held by the company, expected to be settled after more than 12 months.
    - Retirement Benefits Obligations (retirement_benefits_obligations): Future liabilities for employee pensions, severance, or other post-employment benefits.
    - Other Allowances (other_allowances): Reserves set aside for potential future expenses or liabilities, such as warranty claims or contingencies.
    - Deferred Tax Liabilities (deferred_tax_liabilities): Taxes owed due to temporary differences between financial and tax reporting, payable in the future.
    - Other Long-term Liabilities (other_long_term_liabilities): Miscellaneous long-term obligations not classified under specific categories above.

    and TTL Long-Term Liabilities (long_term_liabilities).

    Please note that these values should be extracted from the financial statement.
    In case the value is not available, please set the value to None.
    One value in the financial statement is only mapped to exact one field of this class.
    """

    bonds_debentures: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Bonds & Debentures' in ITAM-G.",
    )
    long_term_loans_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Long-term Loans Payable' in ITAM-G.",
    )
    long_term_loans_payable_to_related_companies: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Long-term Loans Payable to Related Companies' in ITAM-G.",
    )
    lease_obligations: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Lease Obligations' in ITAM-G.",
    )
    long_term_other_accounts_payable: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Long-term Other Accounts Payable' in ITAM-G.",
    )
    long_term_deposits: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Long-term Deposits' in ITAM-G.",
    )
    retirement_benefits_obligations: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Retirement Benefits Obligations' in ITAM-G.",
    )
    other_allowances: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Other Allowances' in ITAM-G.",
    )
    deferred_tax_liabilities: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Deferred Tax Liabilities' in ITAM-G.",
    )
    other_long_term_liabilities: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Other Long-term Liabilities' in ITAM-G.",
    )
    long_term_liabilities: Item | None = Field(
        None,
        description="The total long-term liabilities, this field should equal to total of all other values.",
    )

    def pretty_print(self, exchange_rate: float = 1) -> None:
        values = []
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Item):
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value.value/exchange_rate)} - note: {value.note}"
                    )
                else:
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value/exchange_rate)}"
                    )
        return "\n".join(values)


class Liabilities(BaseModel):
    """
    This class defines values that should be extracted from financial report.
    These values belong to liabilities, which includes:
    - Current Liabilities (current_liabilities)
    - Long-term Liabilities (long_term_liabilities)
    - Total Liabilities (total_liabilities)

    note that to ensure the accuracy of the data, the total liabilities should be the sum of current liabilities and long-term liabilities.
    such that: current_liabilities.current_liabilities + long_term_liabilities.long_term_liabilities = total_liabilities.value

    """

    current_liabilities: CurrentLiabilities | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Current Liabilities' in ITAM-G.",
    )
    long_term_liabilities: LongTermLiabilities | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Long-term Liabilities' in ITAM-G.",
    )
    total_liabilities: Item | None = Field(
        None,
        description="The value of the item in the financial statement that can map to 'Total Liabilities' in ITAM-G.",
    )

    # def __str__(self) -> str:
    #     """return the string representation of the object.
    #     check for all values in the class, if value is None, then skip it.
    #     Otherwise, return the string of the value:
    #     - key: split by '_', capitalize first letter of each word, and join by space.
    #     - value (Item): print value of item, in str format, separated by comma, for example: '100,000', '200,000', '423,123.012', etc.

    #     Returns:
    #         str: the string representation of the object
    #     """
    #     output = []
    #     output += ["***" * 5, "Current Liabilities:"]
    #     if self.current_liabilities is not None:
    #         output.append(str(self.current_liabilities))
    #     output += ["***" * 5, "Long-term Liabilities:"]
    #     if self.long_term_liabilities is not None:
    #         output.append(str(self.long_term_liabilities))
    #     output += ["***" * 5, "Total Liabilities:"]
    #     if self.total_liabilities is not None:
    #         output.append(
    #             f"Total Liabilities: {print_value(self.total_liabilities.value)}"
    #         )
    #     return "\n".join(output)

    def pretty_print(self, exchange_rate: float = 1) -> None:
        """print the values of the class in a pretty format
        check for all values in the class, if value is None, then skip it.
        Otherwise, return the string of the value:
        - key: split by '_', capitalize first letter of each word, and join by space.
        - value (Item): print value of item, in str format, separated by comma, for example: '100,000', '200,000', '423,123.012', etc.

        Args:
            exchange_rate (float): exchange rate to convert the value to the target currency
        """
        output = []
        # output += ["***" * 5, "Current Liabilities:"]
        if self.current_liabilities is not None:
            output.append(
                f"Current Liabilities: {self.current_liabilities.pretty_print(exchange_rate)}"
            )
        # output += ["***" * 5, "Long-term Liabilities:"]
        if self.long_term_liabilities is not None:
            output.append(
                f"Long-term Liabilities: {self.long_term_liabilities.pretty_print(exchange_rate)}"
            )
        # output += ["***" * 5, "Total Liabilities:"]
        if self.total_liabilities is not None:
            output.append(
                f"Total Liabilities: {print_value(self.total_liabilities.value/exchange_rate)}"
            )
        return "\n\n".join(output)


class StakeholdersEquity(BaseModel):
    """This class defines values extracted from financial report about shareholders equity.
    These values belong to shareholders equity, which includes:
    - Capital Stock (capital_stock): The total value of shares issued by the company at their par value.
    - Capital Surplus (capital_surplus): The amount received from shareholders in excess of the par value of the stock.
    - Retained Earnings (retained_earnings): The cumulative net income the company has earned and kept (retained) instead of distributing as dividends.
    - Treasury Stock (treasury_stock): Shares that were issued but later repurchased by the company.
    - Other Shareholders Equity (other_shareholders_equity): All other equity components not covered by the above categories.
    - Total Shareholders Equity (total_shareholders_equity): The total value of shareholders' equity.
    Please note that these values should be extracted from the financial statement.
    """

    capital_stock: Item | None = Field(
        description="The total value of shares issued by the company at their par value",
    )
    capital_surplus: Item | None = Field(
        description="Capital surplus refers to the amount received from shareholders in excess of the par value of the stock.",
    )
    retained_earnings: Item | None = Field(
        description="Retained earnings refers the cumulative net income the company has earned and kept (retained) instead of distributing as dividends",
    )
    treasury_stock: Item | None = Field(
        description="Treasury stocks are shares that were issued but later repurchased by the company",
    )
    other_shareholders_equity: Item | None = Field(
        description="All other equity components not covered by the above categories.",
    )
    total_shareholders_equity: float = Field(
        description="The total value of shareholders' equity.",
    )

    def pretty_print(self, exchange_rate: float = 1) -> None:
        values = []
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, Item):
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value.value/exchange_rate)} - note: {value.note}"
                    )
                else:
                    values.append(
                        f"{key.replace('_', ' ').capitalize()}: {print_value(value/exchange_rate)}"
                    )
        return "\n".join(values)


class LiabilitiesAndStakeholdersEquity(BaseModel):
    """This class defines values that should be extracted from financial report.

    These values belong to liabilities and shareholders equity, which includes:
    - Liabilities (liabilities): The total liabilities of the company.
    - Shareholders Equity (shareholders_equity): The total shareholders equity of the company.
    """

    liabilities: Liabilities = Field(
        description="The liabilities of company extracted from the financial statement",
    )
    shareholders_equity: StakeholdersEquity = Field(
        description="The shareholders equity of company extracted from the financial statement",
    )

    def pretty_print(self, exchange_rate: float = 1) -> None:
        values = []
        if self.liabilities is not None:
            values.append(self.liabilities.pretty_print(exchange_rate))
        if self.shareholders_equity is not None:
            values.append(self.shareholders_equity.pretty_print(exchange_rate))
        return "\n\n".join(values)
