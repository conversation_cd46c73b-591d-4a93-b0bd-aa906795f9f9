from langchain.tools import tool
from loguru import logger

from app.schemas.income import (
    CoreOperatingMetrics,
    Income,
    NonRecurringIncome,
    NonRecurringLoss,
)


@tool
def check_income_sum(income: Income) -> str:
    """Check the accuracy of `income`

    Args:
        income (Income): the instance to be check
    Return:
        str: the message indicates result of checking
    """

    logger.info("Start check income sum")

    check_core_operating_metrics_output = check_core_operating_metrics(
        income.operating_metrics
    )
    check_non_recurring_income_output = check_non_recurring_income(
        income.non_recurring_income
    )
    check_non_recurring_loss_output = check_non_recurring_loss(
        income.non_recurring_loss
    )
    check_total_income_output = check_total_income(income)
    check_output = "\n".join(
        [
            check_core_operating_metrics_output,
            check_non_recurring_income_output,
            check_non_recurring_loss_output,
            check_total_income_output,
        ]
    )
    logger.info("Finish check income sum")
    logger.debug(f"Check income output: {check_output}")
    return check_output


def check_core_operating_metrics(operating_metrics: CoreOperatingMetrics) -> str:
    """Check the accuracy of `operating_metrics`

    Args:
        operating_metrics (CoreOperatingMetrics): the instance to be check
    Return:
        str: the message indicates result of checking
    """
    is_valid = True
    check_output = ""
    if (
        operating_metrics.gross_sales.value - operating_metrics.cost_of_sales.value
        != operating_metrics.gross_profit.value
    ):

        check_output += "Net income is greater than total income, you should check again gross sales (revenue), cost of sales (cost of goods sold), and gross profit (gross loss)"
        is_valid = False
    if (
        operating_metrics.gross_profit.value
        - operating_metrics.operating_expenses.value
        != operating_metrics.operating_income
    ):
        is_valid = False
        check_output += "The value of operating income does not match the difference between gross profit and operating expenses. Please check the values of gross profit and operating expenses again."
    if is_valid:
        check_output += "Core operating metrics is correct"
    else:
        check_output += "Core operating metrics is not correct, please check again"
    return check_output


def check_non_recurring_income(non_recurring_income: NonRecurringIncome) -> str:
    return ""


def check_non_recurring_loss(non_recurring_loss: NonRecurringLoss) -> str:
    return ""


def check_total_income(income: Income) -> str:
    return ""
