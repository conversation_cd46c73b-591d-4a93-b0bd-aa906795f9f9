from typing import List

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from loguru import logger

from app.schemas.table_mapping import IncomeStatementTable, TableMapping
from app.table_matching import FinancialTemplateMatcher
from app.utils.azure_openai_client import azure_openai_chat_model
from app.utils.constants import MAX_RETRIES, RETRY_DELAY, FSConstants
from app.utils.utils import retry_on_rate_limit

from .prompt import filter_ic_table_human_prompt, filter_ic_table_system_prompt

mapper = FinancialTemplateMatcher(llm=azure_openai_chat_model)


async def map_table(
    data: str,
) -> TableMapping:
    """Map markdown tables to corresponding Excel templates.

    Args:
        data: The input data string containing markdown tables, splitted by \n\n

    Returns:
        TableMapping: A structured object containing lists of table IDs mapped to
                     balance sheet and income statement templates
    """

    logger.debug("Start mapping tables to templates ...")
    bs_markdown_tables, bs_table_ids = await mapper.match_excel_template(data)
    return bs_markdown_tables, bs_table_ids


@retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
async def filter_income_statement_tables(
    table_data: str,
    model: AzureChatOpenAI = azure_openai_chat_model,
) -> tuple[str, list[int]]:
    """Filter out the income statement tables from the input data.
    Args:
        table_data (str): The input data string containing markdown tables, splitted by \n\n
    Returns:
        tuple[str, list[int]]: A tuple containing
            - The markdown string of the filtered income statement tables.
            - A list of IDs of the filtered income statement tables.

    """

    with_structured_llm = model.with_structured_output(
        IncomeStatementTable,
    )
    table_md_list = table_data.split(FSConstants.TABLE_SEPARATOR.value)

    # Break markdown by 20 tables per batch
    # TODO: Break batches by tokens (for example, 10k tokens per batch)
    table_batches = [
        table_md_list[i : i + 20] for i in range(0, len(table_md_list), 20)
    ]

    # Handle the case where there are multiple batches of tables
    if len(table_batches) > 1:
        list_messages = [
            [
                SystemMessage(content=filter_ic_table_system_prompt),
                HumanMessage(
                    content=filter_ic_table_human_prompt.format(table_markdown=table_md)
                ),
            ]
            for table_md in table_batches
        ]

        _abatch_output: List[IncomeStatementTable] = await with_structured_llm.abatch(
            list_messages
        )
        ic_ids = [
            int(x.split("/")[-1])
            for _aivoke_output in _abatch_output
            for x in _aivoke_output.table_ids
        ]
    else:
        _aivoke_output: IncomeStatementTable = await with_structured_llm.ainvoke(
            [
                SystemMessage(content=filter_ic_table_system_prompt),
                HumanMessage(
                    content=filter_ic_table_human_prompt.format(
                        table_markdown=table_data
                    )
                ),
            ]
        )

        ic_ids = [int(x.split("/")[-1]) for x in _aivoke_output.table_ids]

    table_markdown_list: list[str] = table_data.split("\n\n")
    ic_table_markdown = "\n\n".join([table_markdown_list[i] for i in ic_ids])

    return ic_table_markdown, ic_ids
