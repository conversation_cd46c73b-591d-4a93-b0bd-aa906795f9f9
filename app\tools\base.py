from langchain.tools import tool


@tool
async def get_sum_values(items: list[float]) -> float:
    """Add up all the values in the list
    This tool is used to evaluate the sum of values in one item of the template.

    Args:
        items (list[float]): List of values to sum up (negative values in case minus)

    Returns:
        float: The sum of the values in the list

    Example:
    1. `Item(id = "operating_expenses, value = 1000, extracted_from = "Employee benefit expenses (200) + Other expenses (700)")`
    ```python
    # use the tool to evaluate the sum of values from `extracted_from` field
    get_sum_values([200, 700]) -> 900, wrong answer
    ```

    2. `Item(id = "gross_profit, value = 401, extracted_from = "Revenue from operations (593) - Cost of Sales (192)")`
    ```python
    get_sum_values([593, -192]) -> 401, correct answer
    ```
    """
    return sum(items)


@tool
async def get_sum_values_income(items: list[float]) -> float:
    """Add up all the values in the list
    This tool is used to evaluate the sum of values in one item of the template.

    Args:
        items (list[float]): List of values to sum up (negative values in case minus)

    Returns:
        float: The sum of the values in the list

    Example:
    1. `Item(id = "operating_expenses, value = 1000, extracted_from = "Employee benefit expenses (200) + Other expenses (700)")`
    ```python
    # use the tool to evaluate the sum of values from `extracted_from` field
    get_sum_values_income([200, 700]) -> 900, wrong answer
    ```

    2. `Item(id = "gross_profit, value = 401, extracted_from = "Revenue from operations (593) - Cost of Sales (192)")`
    ```python
    get_sum_values_income([593, -192]) -> 401, correct answer
    ```

    3. `Item(id = "cost_of_sales", value = 580, extracted_from = "Purchases of Stock in Trade (600) - Change in Inventories of Stock in Trade (-20)")`
    ```python
    get_sum_values_income([600, -20]) -> 580, correct answer
    ```
    """
    return sum(items)
