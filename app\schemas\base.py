import re
from dataclasses import dataclass
from enum import Enum, auto
from functools import reduce
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class SheetType(Enum):
    balance_sheet = auto()
    income_statement = auto()


class ReflectionResult(BaseModel):
    is_valid: bool
    reason: Optional[str] = None
    suggestions: Optional[str] = None
    problem_items: Optional[List[str]] = None


@dataclass
class RecalculationUnit:
    target_item: Dict[str, Any]
    detail_items: List[Dict[str, Any]]

    def _safe_float(self, value: Any) -> float:
        try:
            return float(value)
        except (TypeError, ValueError):
            return 0.0

    def get_target_value(self) -> float:
        """
        Trả về giá trị 'value' trong target_item, dưới dạng float an toàn.
        """
        return self._safe_float(self.target_item.get("value"))

    def sum_details(self) -> float:
        """
        Computes the sum of the 'value' field from all detail items.
        Returns:
            float: Total sum of detail_items' values (ignores non-numeric values).
        """
        return sum(self._safe_float(item.get("value")) for item in self.detail_items)

    def remain_value(self, tolerance: float = 1e-2) -> float:
        """
        Calculates the remaining value needed to match the target value.
        Applies tolerance: if within range, return 0.

        Args:
            tolerance (float): Threshold under which differences are considered negligible.

        Returns:
            float: Adjusted remaining value (0 if within tolerance).
        """
        target_val = self._safe_float(self.target_item.get("value"))
        diff = target_val - self.sum_details()
        return 0.0 if abs(diff) <= tolerance else round(diff, 2)

    def extract_clean_words_from_items(self, structured_response) -> List[str]:
        def _get_object_by_item_id(obj, path: str):
            try:
                return reduce(getattr, path.split("/"), obj)
            except AttributeError:
                return None

        def clean_note(note: str) -> str:
            note = re.sub(r"\([^)]*\)", "", note)  # Remove text in parentheses
            note = note.replace("+", "")  # Remove plus signs
            words = note.strip().split()
            return " ".join(words)  # Join into a single string

        keys = [self.target_item.get("item_id")] + [
            item.get("item_id") for item in self.detail_items
        ]
        items = [_get_object_by_item_id(structured_response, key) for key in keys]

        return [
            clean_note(item.note)
            for item in items
            if item and getattr(item, "note", None)
        ]


class Item(BaseModel):
    """
    The item in the financial statement that mapped to the pre-defined item in ITAM-G.

    Args:
        key: The value of the item in the financial statement. This should be matched with the key in the financial statement. In case the item is calculated by adding two or more items, the key should be the calculation formula.
        value: The value of the item in the financial statement.
        note: (optional) The note of the item in the financial statement, how it is calculated and map to the pre-defined item in ITAM-G.

    Example:
        Item(key="Revenue", value=1000, note="revenue (1000)")
        Item(key="Other tangible assets", value=340, note="land (100) + building (140) + equipment (100)")
        Item(key="TTL non-current liabilities", value=200, note="non-current liabilities (200)")
    """

    key: str | None = Field(
        description="The value of the item in the financial statement. This should be matched with the key in the financial statement. In case the item is calculated by adding two or more items, the key should be the calculation formula., default as None",
    )
    value: float | None = Field(
        description="The value of the item in the financial statement, default as 0"
    )
    note: str | None = Field(
        description="(optional, default as None) The note of the formula of 'other' items indicating the number calculation (e.g. = 12 + 13 + 14)",
    )


class NewItem(BaseModel):
    """
    Item in the financial statement.

    Arguments:
        value: The value of the extracted item in the financial statement, default as 0 (if cannot be extracted or not exist)
        note: The note of the extracted item, indicating the original text in the financial statement.


    For example:
        1. You extract item 'Revenue' with value 1000 from income statement table, then:

        .. code-block:: python
            item = Item(value=1000, note="revenue (1000)")

        2. You extract item for total tangible assets with land (100), building (140), and equipment (100) from balance sheet table, then:
        .. code-block:: python
            item = Item(value=340, note="land (100) + building (140) + equipment (100)")
    """

    value: float = Field(
        description="The value of the extracted item in the financial statement, default as 0 (if cannot be extracted or not exist)"
    )
    note: str | None = Field(
        description="The note of the extracted item, indicating the original text in the financial statement.",
    )
