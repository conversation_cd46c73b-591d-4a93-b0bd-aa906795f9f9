import re
from datetime import datetime

from azure.storage.blob import (
    BlobClient,
    BlobSasPermissions,
    BlobServiceClient,
    generate_blob_sas,
)
from loguru import logger

from app.settings import AzureBlobConfig


def list_blob_files(azure_blob_config: AzureBlobConfig) -> list[str]:
    """
        Get all blob name in a container
    :return:
    """
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    container_client = blob_service_client.get_container_client(
        azure_blob_config.container_name
    )

    blob_files = [blob.name for blob in container_client.list_blobs()]
    blob_service_client.close()
    container_client.close()
    return blob_files


def list_blob_files_by_modified_time(
    azure_blob_config: AzureBlobConfig, modified_time
) -> list[str]:
    """
        Get all blob name in a container
    :return:
    """
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    container_client = blob_service_client.get_container_client(
        azure_blob_config.container_name
    )

    blobs = container_client.list_blobs()
    # Filter by modified time
    if modified_time is not None:
        modified_datetime = datetime.fromisoformat(modified_time.replace("Z", "+00:00"))
        blobs = [
            blob for blob in blobs if blob.get("last_modified") > modified_datetime
        ]
    blob_names = [blob.name for blob in blobs]
    blob_service_client.close()
    container_client.close()
    return blob_names


def get_blob_data_by_name(azure_blob_config: AzureBlobConfig, blob_name):
    """
        Read a file from Azure Blob Storage.
    :param azure_blob_config:
    :param blob_name:
    :return:
    """
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    blob_client = blob_service_client.get_blob_client(
        container=azure_blob_config.container_name, blob=blob_name
    )

    # Download the file as bytes
    file_data = blob_client.download_blob().readall()
    blob_service_client.close()
    blob_client.close()
    return file_data


def get_blob_by_name(azure_blob_config: AzureBlobConfig, blob_name) -> BlobClient:
    """
        Get blob from Azure Blob Storage by name.
    :param azure_blob_config:
    :param blob_name:
    :return:
    """
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    blob_client = blob_service_client.get_blob_client(
        container=azure_blob_config.container_name, blob=blob_name
    )

    return blob_client


def upload_file_content(
    content: bytes, blob_name: str, azure_blob_config: AzureBlobConfig
) -> bool:
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    container_client = blob_service_client.get_container_client(
        azure_blob_config.container_name
    )

    if not container_client.exists():
        container_client.create_container()
        logger.info(f"Created container {azure_blob_config.container_name}.")

    blob_client = container_client.get_blob_client(blob_name)
    blob_client.upload_blob(content, overwrite=True)
    return True


def generate_blob_sas_url(
    blob_name: str, azure_blob_config: AzureBlobConfig, expired_time: datetime
) -> str:
    blob_service_client = BlobServiceClient.from_connection_string(
        azure_blob_config.blob_connection_str
    )
    blob_client = blob_service_client.get_blob_client(
        container=azure_blob_config.container_name, blob=blob_name
    )

    blob_account = extract_key_from_connection_string(azure_blob_config)
    sas_token = generate_blob_sas(
        account_name=blob_account.get("account_name"),
        container_name=azure_blob_config.container_name,
        blob_name=blob_name,
        account_key=blob_account.get("account_key"),
        permission=BlobSasPermissions(read=True),
        expiry=expired_time,
    )
    return f"{blob_client.url}?{sas_token}"


def extract_key_from_connection_string(azure_blob_config: AzureBlobConfig) -> dict:
    connection_str = azure_blob_config.blob_connection_str
    account_name = re.search(r"AccountName=([^;]+)", connection_str).group(1)
    account_key = re.search(r"AccountKey=([^;]+)", connection_str).group(1)
    return {"account_key": account_key, "account_name": account_name}
