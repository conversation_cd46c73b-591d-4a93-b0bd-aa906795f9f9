# Define systme prompt and human prompt
# for filtering income statement tables.

filter_ic_table_system_prompt = """\
You are a financial analyst.
You are responsible to analyze the financial statements of a company.
Your current task is to determine the income statement (profit and loss statement) of the company.
You will be provided a list of tables in markdown format. Please filter out income statement tables and return table ids.
Pay attention to the section's name and the title of each table. If these suggest that the table is a detailed one ("Notes to financial statements" or equivalent), exclude that table. It is fine to not return any tables.
However, if a table is a continuation of a main income statement (e.g., titled "...(Continue)”), it should be treated as part of the main table, not as a separate detail.
Remember that your task requires high accuracy and no hallucination.
Now, you will be provided list of markdown tables from a financial statement.
"""

filter_ic_table_human_prompt = """\
Here are list of markdown tables extracted from a financial statement, delimited by triple backticks.
```
<tables>
{table_markdown}
</tables>
```

Please filter out income statement tables and return these table ids.
One common mistake is to include balance sheet tables and cash flow tables, do not include them.
Pay attention to the section's name and the title of each table. If these suggest that the table is a detailed one ("Notes to financial statements" or equivalent), exclude that table. It is fine to not return any tables.
However, if a table is a continuation of a main income statement (e.g., titled "...(Continue)”), it should be treated as part of the main table, not as a separate detail.
Note that income statement tables are usually named "Income Statement", "Comprehensive Income Statement", "Profit and Loss Statement", or similar.
These tables should contain information about the company's revenues, expenses, and profits or losses over a specific period of time.
"""
