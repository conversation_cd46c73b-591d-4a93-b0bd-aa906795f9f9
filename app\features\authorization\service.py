from fastapi import HTT<PERSON>Exception, Request, status

from app.utils.constants import Message


def require_group(group_name: str):
    def dependency(request: Request):
        user_groups = [group["value"] for group in request.state.group_names]

        # Check if the user is an admin or belongs to the required group
        if group_name in user_groups:
            return
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=Message.MSG_GROUP_PERMISSION_DENIED.format(group_name=group_name),
        )

    return dependency
