from fastapi import status  # noqa
from starlette.exceptions import HTTPException

from app.utils import constants


def check_user_permission_feedback(request, user_id, view_feedback=False):
    # Check if the current user has permission to do this resource
    user_groups = [group["value"] for group in request.state.group_names]
    if request.state.user_id != user_id and (
        constants.GroupPermission.FEEDBACK_MANAGEMENT.name not in user_groups
        or not view_feedback
    ):
        raise NoPermissionException()


class NoPermissionException(HTTPException):
    def __init__(self, message=constants.Message.MSG_PERMISSION_DENIED):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=message)


class ObjectNotFoundException(HTTPException):
    def __init__(self, message=constants.Message.MSG_ITEM_NOT_FOUND):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=message)


class InvalidRequestException(HTTPException):
    def __init__(self, message=constants.Message.MSG_INVALID_REQUEST):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message,
        )
