import asyncio
import io
import json
import traceback
import uuid
from datetime import datetime, timedelta
from typing import IO, List

from fastapi import BackgroundTasks, HTTPException, UploadFile, status
from loguru import logger
from sqlalchemy import and_, false, select
from sqlalchemy.orm import Session, aliased

from app.agents.base import FinancialAnalyzerGraphState
from app.agents.graph import FinancialAnalyzerGraph
from app.data_processing.data_extraction import FinancialPDFExtractor
from app.data_processing.model import DocumentTableMetaData
from app.database.db import (
    insert_multi_rows,
    insert_row,
    search_filter_sort_paginate,
    update_row,
)
from app.features.conversation.model import (
    ConversationCreate,
    ConversationDelete,
    ConversationInfo,
    ConversationItemUpdate,
    ConversationListResponse,
    ConversationResponse,
    ConversationUpdate,
    FinancialConversation,
    FinancialDocument,
    FinancialReport,
    Operator,
    Status,
    UploadedFile,
)
from app.schemas.base import SheetType
from app.schemas.highlights import OutputItem
from app.settings import AzureBlobConfig
from app.utils import azure_blob_util, send_socket_notification
from app.utils.azure_openai_client import azure_openai_chat_model, langfuse_handler
from app.utils.constants import Constants, Message, update_rules
from app.utils.helper import apply_update_rules, map_values_into_sheet, postprocess

conversation_columns = [
    "id",
    "conversation_name",
    "createdTime",
    "updatedTime",
    "status",
    "deleted",
]


class ConversationService:
    def __init__(self):
        self.callbacks = [langfuse_handler] if langfuse_handler else None
        self.template_path: str = "app/template/template.json"

    async def create_new_conversation(
        self,
        conversation_create: ConversationCreate,
        upload_file: UploadFile,
        db_session: Session,
    ) -> ConversationResponse:
        """
        Create a new conversation.
        :param conversation_create: ConversationCreate object including user_id and conversation_name
        :param upload_file: Uploaded file in PDF format
        :param db_session: DB session
        :return:
        """
        pdf_extractor = None
        try:
            file_name = upload_file.filename
            # Upload to blob storage
            content = await upload_file.read()

            unique_file_id = str(uuid.uuid4())
            blob_name = f"{Constants.BLOB_PDF_PREFIX}/{conversation_create.user_id}/{unique_file_id}-{file_name}"

            azure_blob_util.upload_file_content(content, blob_name, AzureBlobConfig())
            logger.debug("Successfully upload file to blob storage.")

            # Processing file and upload metadata to blob
            pdf_extractor = FinancialPDFExtractor()
            data: IO[bytes] = io.BytesIO(content)
            _, table_markdown = (
                await pdf_extractor.initialize_and_extract_table_markdown(data)
            )
            tbl_bb_list = pdf_extractor.get_table_bounding_box()
            tbl_bb_dict_list = list()
            for table_bb in tbl_bb_list:
                base_dict = table_bb.model_dump(exclude={"cells"})
                base_dict["cells"] = [cell.model_dump() for cell in table_bb.cells]
                tbl_bb_dict_list.append(base_dict)

            pdf_metadata = {
                Constants.PDF_TABLE_MARKDOWN: table_markdown,
                Constants.PDF_TABLE_BOUNDING_BOX: tbl_bb_dict_list,
            }
            pdf_metadata_json = json.dumps(pdf_metadata)
            metadata_blob_name = f"{Constants.BLOB_PDF_PREFIX}/{conversation_create.user_id}/{unique_file_id}-{file_name}.json"
            azure_blob_util.upload_file_content(
                pdf_metadata_json.encode("utf-8"), metadata_blob_name, AzureBlobConfig()
            )
            logger.debug("Successfully upload file json metadata to blob storage.")

            # Insert a new record in FinancialDocument table
            financial_document = FinancialDocument(
                user_id=conversation_create.user_id,
                filename=file_name,
                blob_path=blob_name,
                pdf_metadata=metadata_blob_name,  # TODO: adding pdf metadata including table markdown, bounding box.
            )
            saved_financial_doc = insert_row(db_session, financial_document)

            # Insert a new record in FinancialConversation table
            financial_conversation = FinancialConversation(
                user_id=conversation_create.user_id,
                conversation_name=conversation_create.conversation_name,
                document_id=saved_financial_doc.id if saved_financial_doc else None,
            )
            logger.debug("Inserting financial conversation...")
            saved_financial_doc: FinancialConversation = insert_row(
                db_session, financial_conversation
            )
            logger.debug("Successfully insert financial conversation.")

            return ConversationResponse(
                id=saved_financial_doc.id,
                conversation_name=saved_financial_doc.conversation_name,
                document_name=file_name,
                report_name="",  # TODO: Adding excel blob name here
                mapping_metadata="",  # TODO: Adding mapping result here
                status=saved_financial_doc.status,
            )
        except Exception as e:
            raise e
        finally:
            if pdf_extractor:
                await pdf_extractor.close()

    async def create_multi_conversations(
        self,
        conversation_create: List[ConversationCreate],
        db_session: Session,
        user_email: str,
    ) -> List[FinancialConversation]:
        """
        Create a new conversation.
        :param conversation_create: ConversationCreate object including user_id and conversation_name
        :param upload_file: Uploaded file in PDF format
        :param db_session: DB session
        :return:
        """
        try:
            financial_conversations: List[FinancialConversation] = [
                FinancialConversation(
                    user_id=conv.user_id,
                    conversation_name=conv.conversation_name,
                    status=Status.INIT.value,
                )
                for conv in conversation_create
            ]
            financial_conversations: List[FinancialConversation] = insert_multi_rows(
                db_session=db_session,
                obj_tables=financial_conversations,
            )

            return financial_conversations
        except HTTPException as e:
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.FAIL_TO_CREATE_CONVERSATION.value}_{user_email.upper()}"
            )
            logger.error(f"Error while creating a new conversation {e}")
            raise e
        except Exception as e:
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.FAIL_TO_CREATE_CONVERSATION.value}_{user_email.upper()}"
            )
            logger.error(f"Error while creating a new conversation {e}")
            logger.debug(traceback.format_exc())
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=Message.INTERNAL_SERVER_ERROR,
            ) from e

    @staticmethod
    def get_pdf_table_metadata_by_conversation_id(
        conversation_id: int, db_session: Session
    ) -> tuple[str, list[DocumentTableMetaData]]:
        """
        Get table metadata by conversation ID
        :param conversation_id: conversation ID
        :param db_session: DB session
        :return: return a tuple with the first parameter is table markdown string and the second parameter is a list of table bounding boxes
        """
        # Get pdf file metadata blob name
        metadata_blob_path: str = str(
            db_session.query(FinancialDocument.pdf_metadata)
            .join(
                FinancialConversation,
                FinancialConversation.document_id == FinancialDocument.id,
            )
            .filter(FinancialConversation.id == conversation_id)
            .first()[0]
        )
        # Parse metadata to get table markdown, table bounding box
        metadata_data = azure_blob_util.get_blob_data_by_name(
            AzureBlobConfig(), metadata_blob_path
        )
        metadata_json = json.loads(metadata_data)

        table_metadata_models = [
            DocumentTableMetaData(**table_bb)
            for table_bb in metadata_json[Constants.PDF_TABLE_BOUNDING_BOX]
        ]
        return metadata_json[Constants.PDF_TABLE_MARKDOWN], table_metadata_models

    def get_conversations_by_user(
        self, db_session: Session, user_id: int, common_params: dict = {}
    ) -> list[ConversationListResponse]:
        """
        Get all conversation by user_id
        :param common_params:
        :param user_id: The user id
        :param db_session: The database session. Defaults to Depends(get_db).
        """
        query = (
            db_session.query(
                FinancialConversation.id,
                FinancialConversation.conversation_name,
                FinancialConversation.created_time,
                FinancialConversation.updated_time,
                FinancialConversation.status,
                FinancialConversation.deleted,
            )
            .filter(
                and_(
                    FinancialConversation.deleted == false(),
                    FinancialConversation.user_id == user_id,
                )
            )
            .order_by(FinancialConversation.updated_time.desc())
        )
        data = search_filter_sort_paginate(
            model=FinancialConversation.__tablename__, query=query, **common_params
        )
        results = data.get("data", [])
        rows = [dict(zip(conversation_columns, row)) for row in results]
        conversation_list = [ConversationListResponse(**dict(row)) for row in rows]
        return conversation_list

    def get_conversation_by_id(
        self, db_session: Session, conversation_id: int, user_id: int
    ) -> ConversationResponse:
        """
        Get conversation by conversation ID
        :param db_session: The database session. Defaults to Depends(get_db).
        :param conversation_id: The conversation id
        :param user_id: The user id
        """
        # Query conversation by conversation ID
        statement = (
            select(
                FinancialConversation.id,
                FinancialConversation.conversation_name,
                FinancialDocument.filename.label("document_name"),
                FinancialDocument.blob_path.label("document_blob_name"),
                FinancialReport.filename.label("report_name"),
                FinancialReport.blob_path.label("report_blob_name"),
                FinancialConversation.mapping_metadata,
                FinancialConversation.status,
            )
            .where(
                and_(
                    FinancialConversation.deleted == false(),
                    FinancialConversation.user_id == user_id,
                    FinancialConversation.id == conversation_id,
                )
            )
            .join(
                FinancialDocument,
                FinancialConversation.document_id == FinancialDocument.id,
            )
            .outerjoin(
                FinancialReport, FinancialConversation.report_id == FinancialReport.id
            )
        )

        row = db_session.execute(statement).mappings().first()
        conversation: ConversationResponse = (
            ConversationResponse(**dict(row)) if row else None
        )

        # Generate SAS url with 15-mins expiration
        if conversation is not None:
            expired_time = datetime.utcnow() + timedelta(minutes=15)
            document_sas_token = (
                azure_blob_util.generate_blob_sas_url(
                    conversation.document_blob_name, AzureBlobConfig(), expired_time
                )
                if conversation.document_blob_name
                else None
            )
            report_sas_token = (
                azure_blob_util.generate_blob_sas_url(
                    conversation.report_blob_name, AzureBlobConfig(), expired_time
                )
                if conversation.report_blob_name
                else None
            )
            conversation.document_download_url = document_sas_token
            conversation.report_download_url = report_sas_token
            conversation.download_url_expired = expired_time
        return conversation

    @staticmethod
    def _multiply_values(data, multiplier):
        def recurse(obj):
            if isinstance(obj, dict):
                for k, v in obj.items():
                    if k == "expected_value" and isinstance(v, str):
                        try:
                            numeric_val = float(v)
                            obj[k] = str(numeric_val * multiplier)
                        except ValueError:
                            pass  # Skip if the value can't be converted to float
                    else:
                        recurse(v)
            elif isinstance(obj, list):
                for item in obj:
                    recurse(item)

        recurse(data)
        return data

    def update_conversation_by_id(
        self,
        user_id: int,
        conversation_id: int,
        conversation_update: ConversationUpdate,
        db_session: Session,
    ):
        """
        Update conversation by conversation ID
        :param user_id: The user id
        :param conversation_id: The conversation id
        :param conversation_update: Conversation update object including conversation_name and conversion_factor
        :param db_session: The database session
        :return: CovnesationResponse object
        """
        conversation = (
            db_session.query(FinancialConversation)
            .filter(
                FinancialConversation.user_id == user_id,
                FinancialConversation.id == conversation_id,
                FinancialConversation.deleted == false(),
            )
            .first()
        )
        if conversation is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found",
            )
        else:
            # Check if conversion settings have changed
            if (
                conversation_update.conversion_factor != conversation.conversion_factor
                or conversation_update.operator != conversation.operator
            ):
                if conversation.mapping_metadata is not None:
                    logger.info("Converting data ...")
                    data = json.loads(conversation.mapping_metadata)

                    if conversation_update.conversion_factor == 0:
                        raise HTTPException(
                            status_code=422, detail="Conversion factor cannot be zero!"
                        )

                    # Reverse old transformation
                    old_factor = conversation.conversion_factor or 1
                    if conversation.operator == Operator.MULTIPLY.value:
                        multiplier = 1 / old_factor
                    elif conversation.operator == Operator.DIVIDE.value:
                        multiplier = old_factor
                    else:
                        multiplier = 1

                    # Apply new transformation
                    new_factor = conversation_update.conversion_factor
                    if conversation_update.operator == Operator.MULTIPLY.value:
                        multiplier *= new_factor
                    elif conversation_update.operator == Operator.DIVIDE.value:
                        multiplier /= new_factor

                    updated_data = ConversationService._multiply_values(
                        data, multiplier
                    )
                    conversation_update.mapping_metadata = json.dumps(updated_data)

            update_row(db_session, conversation, conversation_update)
            return self.get_conversation_by_id(db_session, conversation_id, user_id)

    def delete_conversation_by_id(
        self, user_id: int, conversation_id: int, db_session: Session
    ) -> bool:
        """
        Delete conversation by conversation ID
        :param user_id: The user id
        :param conversation_id: The conversation id
        :param db_session: The database session
        :return: boolean
        """
        conversation = (
            db_session.query(FinancialConversation)
            .filter(
                FinancialConversation.user_id == user_id,
                FinancialConversation.id == conversation_id,
                FinancialConversation.deleted == false(),
            )
            .first()
        )
        if conversation is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found",
            )
        else:
            update_row(db_session, conversation, ConversationDelete(deleted=True))
            return True

    def get_process_by_user(
        self, db_session: Session, user_id: int, conv_status: str = Status.PROCESS.value
    ) -> List[ConversationInfo]:
        """
        Get all conversation by user_id
        :param common_params:
        :param user_id: The user id
        :param db_session: The database session. Defaults to Depends(get_db).
        """
        logger.info(user_id)
        logger.info(conv_status)
        # Alias for FinancialDocument
        document_alias = aliased(FinancialDocument)

        # Join FinancialConversation with FinancialDocument
        conversations = (
            db_session.query(
                FinancialConversation.id.label("conversation_id"),
                FinancialConversation.user_id,
                FinancialConversation.conversation_name,
                FinancialConversation.conversation_type,
                FinancialConversation.document_id,
                document_alias.filename.label("document_name"),
                FinancialConversation.status,
                FinancialConversation.created_time,
                FinancialConversation.conversion_factor,
                FinancialConversation.operator,
            )
            .outerjoin(
                document_alias,
                FinancialConversation.document_id == document_alias.id,
            )
            .filter(
                FinancialConversation.deleted == false(),
                FinancialConversation.user_id == user_id,
                FinancialConversation.status == conv_status,
            )
            .order_by(FinancialConversation.updated_time.desc())
            .all()
        )

        conversation_info = [
            ConversationInfo(
                conversation_id=conv.conversation_id,
                user_id=conv.user_id,
                conversation_name=conv.conversation_name,
                conversation_type=conv.conversation_type,
                document_id=conv.document_id,
                document_name=conv.document_name,
                status=conv.status,
                createdTime=conv.created_time,
                conversion_factor=conv.conversion_factor,
                operator=conv.operator,
            )
            for conv in conversations
        ]

        return conversation_info

    async def process_financial_statement(
        self,
        upload_file: UploadFile,
        conversation: FinancialConversation,
        user_id: int,
        db_session: Session,
        user_email: str,
    ) -> ConversationInfo:
        """
        Process financial statement and update conversation status

        Args:
            upload_file (UploadFile): The uploaded file in PDF format
            conversation (FinancialConversation): The conversation object
            user_id (int): The user ID
            db_session (Session): The database session

        Raises:
            HTTPException: The conversation is not found
            HTTPException: Error while uploading file to blob storage
            HTTPException: Error while processing financial statement

        Returns:
            ConversationInfo: The conversation object with updated status
        """
        # upload file to blob storage and insert into database
        try:
            file_name = upload_file.filename
            content = await upload_file.read()
            unique_file_id = str(uuid.uuid4())
            # Upload to blob storage
            blob_name = (
                f"{Constants.BLOB_PDF_PREFIX}/{user_id}/{unique_file_id}-{file_name}"
            )
            azure_blob_util.upload_file_content(content, blob_name, AzureBlobConfig())
            logger.debug("Successfully upload file to blob storage.")

            financial_document = insert_row(
                db_session,
                FinancialDocument(
                    user_id=user_id, filename=file_name, blob_path=blob_name
                ),
            )

            # update conversation status to processing and set the document id
            conversation.document_id = financial_document.id
            conversation.status = Status.PROCESS.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.PROCESS.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            conversation.conversation_name = file_name

            db_session.commit()
        except Exception as e:
            logger.error(f"Error while uploading file to blob storage: {e}")
            conversation.status = Status.FAIL_TO_UPLOAD.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.FAIL_TO_UPLOAD.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            db_session.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error while uploading file to blob storage",
            )

        # process FS data
        file_bytes = io.BytesIO(content)
        try:
            fs_agent = FinancialAnalyzerGraph(model=azure_openai_chat_model)
            output = await fs_agent.app.ainvoke(
                input=FinancialAnalyzerGraphState(
                    file_bytes=file_bytes,
                    number_of_tool_calls=0,
                    max_retries_per_subgraph=1,
                    income_table_ids=[],
                    balance_table_ids=[],
                ),
                config={"callbacks": self.callbacks},
            )

            # Update to save into database
            balance_sheet_items = [
                item.__dict__
                for item in output["asset_response"] + output["liabilities_response"]
            ]
            income_statement_items = [
                item.__dict__ for item in output["income_response"]
            ]

            # data
            json_response = [
                {
                    "template": SheetType.balance_sheet.name,
                    "data": balance_sheet_items,
                },
                {
                    "template": SheetType.income_statement.name,
                    "data": income_statement_items,
                },
            ]
            with open(self.template_path, "r") as template_file:
                template: list[dict] = json.load(template_file)

            for item in json_response:
                if item.get("template") == SheetType.balance_sheet.name:
                    template = map_values_into_sheet(
                        sheet_id=SheetType.balance_sheet.value,
                        item_list=[
                            OutputItem(**_item) for _item in item.get("data", [])
                        ],
                        template=template,
                    )
                elif item.get("template") == SheetType.income_statement.name:
                    template = map_values_into_sheet(
                        sheet_id=SheetType.income_statement.value,
                        item_list=[
                            OutputItem(**_item) for _item in item.get("data", [])
                        ],
                        template=template,
                    )

            template = apply_update_rules(template, update_rules)
            conversation.mapping_metadata = json.dumps(template)

            conversation.status = Status.SUCCESS.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.SUCCESS.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            db_session.commit()

            conversation_info = ConversationInfo(
                conversation_id=conversation.id,
                user_id=conversation.user_id,
                conversation_name=conversation.conversation_name,
                conversation_type=conversation.conversation_type,
                document_id=conversation.document_id,
                document_name=file_name,
                status=conversation.status,
                mapping_metadata=template,
            )
            return conversation_info
        except Exception as e:
            logger.error(f"Error while processing financial statement: {e}")
            conversation.status = Status.ERROR.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.ERROR.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            db_session.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error while processing financial statement",
            )

    def get_conversation_detail_by_id(
        self,
        conversation_id: int,
        db_session: Session,
        template_path: str = "app/template/template.json",
    ) -> ConversationInfo:
        """
        Get conversation detail by conversation ID

        Args:
            conversation_id (int): The conversation ID
            db_session (Session): The database session
        Returns:
            FinancialConversation: The conversation object
        Raises:
            HTTPException: If the conversation is not found
        """

        # Load the template from the JSON file
        with open(template_path, "r") as template_file:
            template: list[dict] = json.load(template_file)

        template = postprocess(template)

        # Fetch the conversation from the database
        conversation = (
            db_session.query(FinancialConversation)
            .filter(
                FinancialConversation.id == conversation_id,
                FinancialConversation.deleted == false(),
            )
            .first()
        )
        if conversation is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=Message.MSG_CONVERSATION_NOT_FOUND,
            )

        # Get document name from the FinancialDocument table, query all, get the last file inserted
        document = (
            db_session.query(FinancialDocument)
            .filter(
                FinancialDocument.id == conversation.document_id,
                FinancialDocument.deleted == false(),
            )
            .order_by(FinancialDocument.created_time.desc())
            .first()
        )
        if document is None:
            document_name = ""
        else:
            document_name = document.filename

        conversation_info = ConversationInfo(
            conversation_id=conversation.id,
            user_id=conversation.user_id,
            conversation_name=conversation.conversation_name,
            conversation_type=conversation.conversation_type,
            document_id=conversation.document_id,
            document_name=document_name,
            status=conversation.status,
            conversion_factor=conversation.conversion_factor,
            createdTime=conversation.created_time,
            operator=conversation.operator,
        )

        # Handle empty or invalid mapping_metadata
        if not conversation.mapping_metadata:
            conversation_info.mapping_metadata = template
            return conversation_info

        try:
            mapping_data: list[dict] = json.loads(conversation.mapping_metadata)
            mapping_data = postprocess(mapping_data)
        except json.JSONDecodeError:
            logger.error(
                f"Invalid JSON in mapping_metadata for conversation {conversation.id}"
            )
            conversation_info.mapping_metadata = template
            return conversation_info
        # check if mapping_data[0] has attribute 'sheet_id'
        if "sheet_id" in mapping_data[0]:
            logger.info("Mapping data has been updated before.")
            conversation_info.mapping_metadata = mapping_data
            return conversation_info

        logger.info("Updating template with mapping data.")
        for item in mapping_data:
            if item.get("template") == SheetType.balance_sheet.name:
                template = map_values_into_sheet(
                    sheet_id=SheetType.balance_sheet.value,
                    item_list=[OutputItem(**item) for item in item.get("data", [])],
                    template=template,
                )
            elif item.get("template") == SheetType.income_statement.name:
                template = map_values_into_sheet(
                    sheet_id=SheetType.income_statement.value,
                    item_list=[OutputItem(**item) for item in item.get("data", [])],
                    template=template,
                )

        # Convert the template to JSON string and update the conversation
        conversation_info.mapping_metadata = postprocess(template)

        # Return the updated conversation info
        return conversation_info

    def update_item_values_by_conversation_id(
        self,
        user_id: int,
        conversation_id: int,
        conversation_item_update: ConversationItemUpdate,
        db_session: Session,
    ) -> FinancialConversation:
        """Update item values by conversation ID

        Args:
            user_id (int): user id
            conversation_id (int): conversation id
            conversation_item_update (ConversationItemUpdate): _description_
            db_session (Session): _description_

        Raises:
            HTTPException: _description_

        Returns:
            FinancialConversation: _description_
        """
        conversation = (
            db_session.query(FinancialConversation)
            .filter(
                FinancialConversation.id == conversation_id,
                FinancialConversation.deleted == false(),
            )
            .first()
        )
        if conversation is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=Message.MSG_CONVERSATION_NOT_FOUND,
            )

        # Handle empty or invalid mapping_metadata
        if not conversation.mapping_metadata:
            # conversation.mapping_metadata = template
            logger.error("Mapping metadata is empty")
            return conversation

        try:
            mapping_data: list[dict] = json.loads(conversation.mapping_metadata)
        except json.JSONDecodeError:
            logger.error(
                f"Invalid JSON in mapping_metadata for conversation {conversation.id}"
            )
            # conversation.mapping_metadata = template
            return conversation

        # Update item values in the mapping data
        for item in conversation_item_update.items:
            sheet_id, screen_id, item_no = (
                int(item.sheet_id),
                int(item.screen_id),
                int(item.item_no),
            )
            screen = mapping_data[sheet_id - 1]["screens"][screen_id - 1]
            item_update = screen["items"][item_no - 1]
            item_update["value"] = item.value

        # update mapping_data to conversation
        conversation.mapping_metadata = json.dumps(mapping_data)
        db_session.commit()
        conversation.mapping_metadata = mapping_data
        return conversation

    async def _upload_file_to_blob_storage(
        self,
        file_names: List[str],
        contents: List[bytes],
        # user_id: int,
        db_session: Session,
        conversations: List[FinancialConversation],
        user_email: str,
    ) -> List[UploadedFile]:
        async def helper(
            file_name: str, content: bytes, conversation: FinancialConversation
        ):
            try:
                user_id = conversation.user_id
                unique_file_id = str(uuid.uuid4())
                # Upload to blob storage
                blob_name = f"{Constants.BLOB_PDF_PREFIX}/{user_id}/{unique_file_id}-{file_name}"
                azure_blob_util.upload_file_content(
                    content, blob_name, AzureBlobConfig()
                )
                logger.debug("Successfully upload file to blob storage.")

                financial_document = insert_row(
                    db_session,
                    FinancialDocument(
                        user_id=user_id, filename=file_name, blob_path=blob_name
                    ),
                )

                # update conversation status to processing and set the document id
                conversation.document_id = financial_document.id
                conversation.status = Status.PROCESS.value
                await send_socket_notification(
                    f"BP_ANALYSIS_{Status.PROCESS.value}_{user_email.upper()}",
                    {
                        "filename": conversation.conversation_name,
                        "conversation_id": conversation.id,
                    },
                )

                db_session.commit()

                return UploadedFile(file_name=file_name, content=content)

            except Exception as e:
                logger.error(
                    f"Error while uploading file {file_name} to blob storage: {e}"
                )
                conversation.status = Status.FAIL_TO_UPLOAD.value
                await send_socket_notification(
                    f"BP_ANALYSIS_{Status.FAIL_TO_UPLOAD.value}_{user_email.upper()}",
                    {
                        "filename": conversation.conversation_name,
                        "conversation_id": conversation.id,
                    },
                )
                db_session.commit()
                return None

        tasks = [
            helper(file_name, content, conversation)
            for file_name, content, conversation in zip(
                file_names, contents, conversations
            )
        ]

        results = await asyncio.gather(*tasks, return_exceptions=False)
        successful_uploads = [res for res in results if res is not None]

        return successful_uploads

    async def _extract_financial_statement(
        self,
        content: bytes,
        conversation: FinancialConversation,
        db_session: Session,
        user_email: str,
    ) -> ConversationInfo:
        file_name = conversation.conversation_name
        file_bytes = io.BytesIO(content)
        try:
            fs_agent = FinancialAnalyzerGraph(model=azure_openai_chat_model)
            output = await fs_agent.app.ainvoke(
                input=FinancialAnalyzerGraphState(
                    file_bytes=file_bytes,
                    number_of_tool_calls=0,
                    max_retries_per_subgraph=1,
                    income_table_ids=[],
                    balance_table_ids=[],
                ),
                config={"callbacks": self.callbacks},
            )

            if not output["map_table_response"]:
                logger.debug("Document is unrelated.")
                conversation.status = Status.INVALID.value
                await send_socket_notification(
                    f"BP_ANALYSIS_{Status.INVALID.value}_{user_email.upper()}",
                    {
                        "filename": conversation.conversation_name,
                        "conversation_id": conversation.id,
                    },
                )
                db_session.commit()
                return None

            # Update to save into database
            balance_sheet_items = [
                item.__dict__
                for item in output["asset_response"] + output["liabilities_response"]
            ]
            income_statement_items = [
                item.__dict__ for item in output["income_response"]
            ]

            # data
            json_response = [
                {
                    "template": SheetType.balance_sheet.name,
                    "data": balance_sheet_items,
                },
                {
                    "template": SheetType.income_statement.name,
                    "data": income_statement_items,
                },
            ]
            with open(self.template_path, "r") as template_file:
                template: list[dict] = json.load(template_file)

            for item in json_response:
                if item.get("template") == SheetType.balance_sheet.name:
                    template = map_values_into_sheet(
                        sheet_id=SheetType.balance_sheet.value,
                        item_list=[
                            OutputItem(**_item) for _item in item.get("data", [])
                        ],
                        template=template,
                    )
                elif item.get("template") == SheetType.income_statement.name:
                    template = map_values_into_sheet(
                        sheet_id=SheetType.income_statement.value,
                        item_list=[
                            OutputItem(**_item) for _item in item.get("data", [])
                        ],
                        template=template,
                    )

            template = apply_update_rules(template, update_rules)
            conversation.mapping_metadata = json.dumps(template)

            conversation.status = Status.SUCCESS.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.SUCCESS.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            db_session.commit()

            conversation_info = ConversationInfo(
                conversation_id=conversation.id,
                user_id=conversation.user_id,
                conversation_name=conversation.conversation_name,
                conversation_type=conversation.conversation_type,
                document_id=conversation.document_id,
                document_name=file_name,
                status=conversation.status,
                mapping_metadata=template,
            )
            return conversation_info
        except Exception as e:
            logger.error(f"Error while processing financial statement: {e}")
            conversation.status = Status.ERROR.value
            await send_socket_notification(
                f"BP_ANALYSIS_{Status.ERROR.value}_{user_email.upper()}",
                {
                    "filename": conversation.conversation_name,
                    "conversation_id": conversation.id,
                },
            )
            db_session.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error while processing financial statement",
            )

    async def _process_one_fs(
        self,
        content: bytes,
        file_name: str,
        user_id: int,
        db_session: Session,
        conversation: FinancialConversation,
        user_email: str,
    ) -> ConversationInfo:
        conversation = (
            db_session.query(FinancialConversation)
            .filter_by(id=conversation.id, user_id=user_id)
            .first()
        )
        if conversation is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=Message.MSG_CONVERSATION_NOT_FOUND,
            )

        await self._upload_file_to_blob_storage(
            content=content,
            file_name=file_name,
            db_session=db_session,
            conversation=conversation,
            user_email=user_email,
        )
        conversation_info = await self._extract_financial_statement(
            content=content,
            conversation=conversation,
            db_session=db_session,
            user_email=user_email,
        )

        return conversation_info

    async def _process_multi_fs(
        self,
        conversations: List[FinancialConversation],
        user_email: str,
        db_session: Session,
        upload_files: List[UploadedFile] = None,
    ):
        conversations = (
            db_session.query(FinancialConversation)
            .filter(
                FinancialConversation.id.in_([conv.id for conv in conversations]),
                FinancialConversation.user_id.in_(
                    [conv.user_id for conv in conversations]
                ),
                FinancialConversation.deleted == false(),
            )
            .all()
        )
        if conversations is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=Message.MSG_CONVERSATION_NOT_FOUND,
            )

        if upload_files:
            upload_files = await self._upload_file_to_blob_storage(
                file_names=[uf.file_name for uf in upload_files],
                contents=[uf.content for uf in upload_files],
                # user_id=conversations[0].user_id,  # Assuming all conversations belong to the same user
                db_session=db_session,
                conversations=conversations,
                user_email=user_email,
            )
        else:
            upload_files = []
            for conv in conversations:
                try:
                    file = UploadedFile(
                        content=azure_blob_util.get_blob_data_by_name(
                            azure_blob_config=AzureBlobConfig(),
                            blob_name=conv.document.blob_path,
                        )
                    )
                    upload_files.append(file)
                except Exception as e:
                    logger.error(
                        f"Failed to get data from blob storage for file {conv.conversation_name}: {e}"
                    )
                    conv.status = Status.ERROR.value
                    await send_socket_notification(
                        f"BP_ANALYSIS_{Status.ERROR.value}_{user_email.upper()}",
                        {
                            "filename": conv.conversation_name,
                            "conversation_id": conv.id,
                        },
                    )
                    db_session.commit()
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Error while reading uploaded file.",
                    )

        conversation_info: List[ConversationInfo] = []
        for upload_file, conversation in zip(upload_files, conversations):
            try:
                info = await self._extract_financial_statement(
                    content=upload_file.content,
                    conversation=conversation,
                    db_session=db_session,
                    user_email=user_email,
                )
                conversation_info.append(info)
            except Exception as e:
                logger.error(f"Error processing {conversation.conversation_name}: {e}")
                conversation_info.append(None)

        return conversation_info

    async def _process_fs_in_background(
        self,
        background_tasks: BackgroundTasks,
        conversations: List[FinancialConversation],
        user_email: str,
        db_session: Session,
        upload_files: List[UploadedFile] = None,
    ):

        # for upload_file, conv in zip(upload_files, conversations):
        #     background_tasks.add_task(
        #         self._process_one_fs,
        #         content=upload_file.content,
        #         file_name=upload_file.file_name,
        #         user_id=conv.user_id,
        #         db_session=db_session,
        #         conversation=conv,
        #         user_email=user_email,
        #     )
        background_tasks.add_task(
            self._process_multi_fs,
            conversations=conversations,
            user_email=user_email,
            db_session=db_session,
            upload_files=upload_files,
        )

    async def process_financial_statement_v2(
        self,
        user_id: int,
        db_session: Session,
        user_email: str,
        background_tasks: BackgroundTasks,
        upload_files: List[UploadFile] = None,
        conversation_id: int = None,
    ):
        if not conversation_id:  # Create new conversation
            conversation_create: List[ConversationCreate] = [
                ConversationCreate(user_id=user_id, conversation_name=uf.filename)
                for uf in upload_files
            ]
            financial_conversations: List[FinancialConversation] = (
                await self.create_multi_conversations(
                    conversation_create=conversation_create,
                    db_session=db_session,
                    user_email=user_email,
                )
            )

            uploaded_file: List[UploadedFile] = [
                UploadedFile(content=await uf.read(), file_name=uf.filename)
                for uf in upload_files
            ]
        else:  # Query existing conversation
            financial_conversation: FinancialConversation = (
                db_session.query(FinancialConversation)
                .filter(
                    FinancialConversation.id == conversation_id,
                    FinancialConversation.user_id == user_id,
                    FinancialConversation.deleted == false(),
                )
                .first()
            )
            if not financial_conversation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=Message.MSG_CONVERSATION_NOT_FOUND,
                )
            financial_conversation.status = Status.PROCESS.value

            db_session.commit()
            financial_conversations = [financial_conversation]

            # TODO: Fetch file content from blob storage using conversation_id and document_id
            uploaded_file = []

        # Process financial statements in the background
        await self._process_fs_in_background(
            background_tasks=background_tasks,
            conversations=financial_conversations,
            user_email=user_email,
            db_session=db_session,
            upload_files=uploaded_file,
        )

        conversation_response = [
            ConversationResponse(
                id=financial_conversation.id,
                user_id=financial_conversation.user_id,
                conversation_name=financial_conversation.conversation_name,
                status=financial_conversation.status,
                deleted=financial_conversation.deleted,
                created_at=financial_conversation.created_time,
                updated_at=financial_conversation.updated_time,
            )
            for financial_conversation in financial_conversations
        ]

        return conversation_response
