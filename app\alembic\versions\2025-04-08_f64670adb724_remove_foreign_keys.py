"""remove foreign keys

Revision ID: f64670adb724
Revises: 29255c5e0391
Create Date: 2025-04-08 14:29:32.171458

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f64670adb724"
down_revision = "29255c5e0391"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "FinancialConversation",
        "document_id",
        existing_type=sa.INTEGER(),
        nullable=True,
    )
    op.alter_column(
        "FinancialConversation", "report_id", existing_type=sa.INTEGER(), nullable=True
    )
    op.drop_constraint(
        "FinancialConversation_document_id_fkey",
        "FinancialConversation",
        type_="foreignkey",
    )
    op.drop_constraint(
        "FinancialConversation_report_id_fkey",
        "FinancialConversation",
        type_="foreignkey",
    )
    op.drop_constraint(
        "FinancialReport_document_id_fkey", "FinancialReport", type_="foreignkey"
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        "FinancialReport_document_id_fkey",
        "FinancialReport",
        "FinancialDocument",
        ["document_id"],
        ["id"],
    )
    op.create_foreign_key(
        "FinancialConversation_report_id_fkey",
        "FinancialConversation",
        "FinancialReport",
        ["report_id"],
        ["id"],
    )
    op.create_foreign_key(
        "FinancialConversation_document_id_fkey",
        "FinancialConversation",
        "FinancialDocument",
        ["document_id"],
        ["id"],
    )
    op.alter_column(
        "FinancialConversation", "report_id", existing_type=sa.INTEGER(), nullable=False
    )
    op.alter_column(
        "FinancialConversation",
        "document_id",
        existing_type=sa.INTEGER(),
        nullable=False,
    )
    # ### end Alembic commands ###
