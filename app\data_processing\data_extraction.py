from typing import IO

from azure.ai.documentintelligence.aio import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import AnalyzeResult, DocumentContentFormat
from azure.core.credentials import AzureKeyCredential
from loguru import logger

from app.data_processing.doc_intel_result_handler import CustomTextSplitter
from app.data_processing.model import (
    DocumentTableCellMetadata,
    DocumentTableMetaData,
    Table,
)
from app.data_processing.utils import get_page_rotation
from app.settings import DocIntelConfig
from app.utils.constants import FSConstants


class FinancialPDFExtractor:
    def __init__(self):
        self.document_intel_client = DocumentIntelligenceClient(
            endpoint=DocIntelConfig.doc_intel_endpoint,
            credential=AzureKeyCredential(DocIntelConfig.doc_intel_key),
        )
        self.table_list = []

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    async def close(self):
        await self.document_intel_client.close()

    async def initialize_and_extract_table_markdown(
        self, pdf_data: IO[bytes]
    ) -> tuple[list[Table], str]:
        """
        Extract table markdown from the pdf file.
        Args:
            pdf_data (IO[bytes]): data of the PDF files in form of bytes
        Return: a tuple with the first element is table list containing each table information, and the second one is the markdown of all table in form of string.
        """
        logger.debug("Start extracting document using DI ...")
        page_rotation = get_page_rotation(pdf_data)

        poller = await self.document_intel_client.begin_analyze_document(
            "prebuilt-layout", body=pdf_data, output_content_format=DocumentContentFormat.MARKDOWN
        )
        result: AnalyzeResult = await poller.result()
        logger.debug("Finish extracting document using DI ...")

        text_splitter = CustomTextSplitter()
        self.table_list: list[Table] = await text_splitter.construct_data(
            analyze_result=result, page_rotation=page_rotation
        )

        formatted_tables = [table.description for table in self.table_list]
        table_markdown = FSConstants.TABLE_SEPARATOR.value.join(formatted_tables)
        await self.close()
        return self.table_list, table_markdown

    def get_table_markdown_by_ids(self, ids: list[str]) -> tuple[list[Table], str]:
        """
        Get table markdown of specific tables.
        Args:
            ids: a list of table ids.
        Return: return a tuple with the first element is table list containing each table information, and the second one is the markdown of all table in form of string.
        """
        tmp_table_list = list(filter(lambda table: table.id in ids, self.table_list))

        formatted_tables = [table.description for table in tmp_table_list]
        table_markdown = "\n\n".join(formatted_tables)
        return tmp_table_list, table_markdown

    def get_table_bounding_box(
        self, ids: list[str] = None
    ) -> list[DocumentTableMetaData]:
        """
        Get the bounding box of the tables by IDs.
        Args: ids: A list of table ids, If None return all tables.
        Return: a list of dict including table ID, page number, and list of coordination [x1,y1,x2,y2,x3,y3,x4,y4] in INCH
        """
        if ids is not None:
            tmp_table_list = list(
                filter(lambda table: table.id in ids, self.table_list)
            )
        else:
            tmp_table_list = self.table_list

        table_bbox_list: list[DocumentTableMetaData] = []
        for table in tmp_table_list:
            cells = [
                DocumentTableCellMetadata(
                    page_number=cell.bounding_regions[0].page_number,
                    polygon=cell.bounding_regions[0].polygon,
                    row_index=cell.row_index,
                    column_index=cell.column_index,
                    content=cell.content,
                )
                for cell in table.cells
            ]

            table_bbox_list.append(
                DocumentTableMetaData(
                    table_id=table.id,
                    bounding_box=table.bounding_box.__dict__,
                    page_number=table.bounding_regions[0].page_number,
                    polygon=table.bounding_regions[0].polygon,
                    cells=cells,
                    column_count=table.column_count,
                    row_count=table.row_count,
                    reconstructed=table.reconstructed,
                )
            )
        return table_bbox_list

    @staticmethod
    def get_table_markdown_from_table_list(
        table_list: list[Table], ids: list[str]
    ) -> tuple[list[Table], str]:
        """
        Get table markdown of specific tables.
        Args:
            ids: a list of table ids.
            table_list: list of tables extracted from the PDF file
        Return: return a tuple with the first element is table list containing each table information, and the second one is the markdown of all table in form of string.
        """
        tmp_table_list = list(filter(lambda table: table.id in ids, table_list))

        formatted_tables = [table.description for table in tmp_table_list]
        table_markdown = "\n\n".join(formatted_tables)
        return tmp_table_list, table_markdown

    @staticmethod
    def get_table_bounding_box_from_table_list(
        table_list: list[Table], ids: list[str] = None
    ) -> list[(str, int, list[int])]:
        """
        Get the bounding box of the tables by IDs.
        Args:
            ids: A list of table ids, If None return all tables.
            table_list: list of tables extracted from the PDF file.
        Return: a list of dict including table ID, page number, and list of coordination [x1,y1,x2,y2,x3,y3,x4,y4] in INCH
        """
        if ids is not None:
            tmp_table_list = list(filter(lambda table: table.id in ids, table_list))
        else:
            tmp_table_list = table_list

        table_bbox_list: list[DocumentTableMetaData] = []
        for table in tmp_table_list:
            cells = [
                DocumentTableCellMetadata(
                    page_number=cell.bounding_regions[0].page_number,
                    polygon=cell.bounding_regions[0].polygon,
                    row_index=cell.row_index,
                    column_index=cell.column_index,
                    content=cell.content,
                )
                for cell in table.cells
            ]

            table_bbox_list.append(
                DocumentTableMetaData(
                    table_id=table.id,
                    bounding_box=table.bounding_box.__dict__,
                    page_number=table.bounding_regions[0].page_number,
                    polygon=table.bounding_regions[0].polygon,
                    cells=cells,
                    column_count=table.column_count,
                    row_count=table.row_count,
                    reconstructed=table.reconstructed,
                )
            )
        return table_bbox_list
