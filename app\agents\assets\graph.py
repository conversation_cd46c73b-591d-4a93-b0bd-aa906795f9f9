from functools import reduce
from itertools import combinations
from typing import Literal

from langchain_core.messages import AIMessage, AnyMessage, HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import Too<PERSON><PERSON><PERSON>, create_react_agent
from langgraph.types import Command
from loguru import logger
from pydantic import BaseModel

from app.agents.base import AssetsGraphState, Checkpointer, SubGraphState
from app.agents.highlights.utils import convert_to_dataframe, transform_to_output_item
from app.schemas.base import NewItem, RecalculationUnit, SheetType
from app.schemas.new_assets import (
    Assets,
    CurrentAssets,
    IntangibleFixedAssets,
    Inventories,
    InvestmentAssets,
    TangibleFixedAssets,
)
from app.tools.assets import check_asset_sum
from app.tools.base import get_sum_values
from app.utils.azure_openai_client import azure_openai_chat_model
from app.utils.constants import BATCH_SIZE_TOOL_CALLS, MAX_RETRIES, RETRY_DELAY
from app.utils.helper import map_values_into_sheet, recalculation_builder
from app.utils.utils import retry_on_rate_limit

from .prompt import (
    BS_HUMAN_PROMPT,
    BS_SYSTEM_PROMPT,
    RECALCUALTE_HUMAN_PROMPT,
    RECALCUALTE_SUMMARY_TEMPLATE,
    RECALCUALTE_SYSTEM_PROMPT,
    evaluate_human_prompt,
    evaluate_system_prompt,
)


def create_asset_agent(
    tools: list = [check_asset_sum],
    model: AzureChatOpenAI = azure_openai_chat_model,
    response_format: type[BaseModel] = Assets,
    checkpointer: Checkpointer | None = None,
    max_retries: int = 2,
) -> CompiledStateGraph:
    workflow = StateGraph(SubGraphState)

    tool_node = ToolNode(tools, name="tool_node")
    llm_with_tool = model.bind_tools(list(tool_node.tools_by_name.values()))
    llm_with_structured = model.with_structured_output(response_format)

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def call_model(
        state: SubGraphState,
    ) -> Literal["generate_structured_output", "tool_node"]:
        return Command(goto="generate_structured_output", update={})
        if state["number_of_tool_calls"] >= max_retries:
            logger.debug(
                f"Go to generate_structured_output since #tool_call >= {max_retries}"
            )
            return Command(goto="generate_structured_output")

        messages = state["messages"]
        response: AIMessage = await llm_with_tool.ainvoke(input=messages)

        if response.tool_calls:
            if response.tool_calls[0]["name"] == tools[0].name:

                logger.debug(f"Go to {tools[0].name}")
                return Command(
                    goto="tool_node",
                    update={
                        "messages": [response],
                        "number_of_tool_calls": state["number_of_tool_calls"] + 1,
                    },
                )
            logger.debug(f"Use tool: {response.tool_calls[0]['name']}")
            return Command(
                goto="tool_node",
                update={
                    "messages": [response],
                },
            )
        logger.debug("Go to generate_structured_output, finish task")
        return Command(
            goto="generate_structured_output", update={"messages": [response]}
        )

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def generate_structured_output(state: SubGraphState) -> Literal["END"]:
        messages = state["messages"]
        logger.debug("Start generate structured output")
        output = await llm_with_structured.ainvoke(input=messages)
        logger.debug(f"output: {output}")
        return Command(goto="END", update={"structured_response": output})

    workflow.add_node("agent", call_model)
    workflow.add_node("generate_structured_output", generate_structured_output)
    workflow.add_node(tool_node)
    workflow.set_entry_point("agent")
    workflow.add_edge("tool_node", "agent")

    app = workflow.compile(checkpointer=checkpointer)

    return app


def get_assets_agent_messages(data: str, **kwargs) -> list[AnyMessage]:
    """Generate default message for asset agent

    Args:
        data (str): the financial data extracted from pdf file

    Returns:
        list[AnyMessage]: list of messages
    """
    messages = [
        SystemMessage(content=BS_SYSTEM_PROMPT),
        HumanMessage(content=BS_HUMAN_PROMPT.format(table_markdown=data)),
    ]
    return messages


def create_assets_agent_v2(
    model: AzureChatOpenAI = azure_openai_chat_model,
) -> CompiledStateGraph:
    """Create a Assets agent graph.

    Returns:
        A compile graph for Assets agent.
    """
    llm_with_structured = model.with_structured_output(
        Assets,
        include_raw=True,
    )
    evaluate_agent = create_react_agent(
        model=model,
        tools=[get_sum_values],
    )

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def generate_response(state: AssetsGraphState) -> dict:
        """Generate the response from the agent."""
        logger.debug("Assets agent generates response")
        messages = state["messages"]
        output: dict = await llm_with_structured.ainvoke(
            messages,
            # config={"callbacks": [langfuse_handler]},
        )
        raw_output = output["raw"]
        return {
            "messages": [raw_output],
            "structured_response": output["parsed"],
        }

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def evaluate_response(state: AssetsGraphState) -> dict:
        """Evaluate the response from the agent."""

        @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
        async def run_batch(messages):
            return await evaluate_agent.ainvoke({"messages": messages})

        structured_response = state["structured_response"]
        if not structured_response:
            return {}

        all_messages = []
        assets_output_items = transform_to_output_item(structured_response)
        assets_df = convert_to_dataframe(assets_output_items)
        for start in range(0, len(assets_df), BATCH_SIZE_TOOL_CALLS):
            batch_df = assets_df.iloc[start : start + BATCH_SIZE_TOOL_CALLS]
            batch_csv = batch_df.to_csv(index=False)
            batch_messages = [
                SystemMessage(content=evaluate_system_prompt),
                HumanMessage(
                    content=evaluate_human_prompt.format(
                        table_markdown=state["data"],  # optional to slice this too
                        results=batch_csv,
                    )
                ),
            ]

            logger.debug(
                f"Evaluating assets batch {start // BATCH_SIZE_TOOL_CALLS + 1}"
            )
            try:
                output = await run_batch(batch_messages)
                if isinstance(output["messages"][-1], AIMessage):
                    all_messages.append(
                        HumanMessage(content=output["messages"][-1].content)
                    )
            except Exception as e:
                logger.error(f"Batch {start} failed: {e}")

        return {
            "messages": all_messages,
            "is_evaluated": True,
        }

    # Define the workflow
    workflow = StateGraph(AssetsGraphState)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("evaluate_response", evaluate_response)
    workflow.add_conditional_edges(
        "generate_response",
        lambda state: END if state.get("is_evaluated", False) else "evaluate_response",
        [END, "evaluate_response"],
    )
    workflow.add_edge("evaluate_response", "generate_response")
    workflow.set_entry_point("generate_response")
    app = workflow.compile()

    return app


def create_assets_agent_v3(
    model: AzureChatOpenAI = azure_openai_chat_model,
) -> CompiledStateGraph:
    """Create an Assets agent graph with recalculation logic (no evaluation)."""

    llm_with_structured = model.with_structured_output(
        Assets,
        include_raw=True,
    )

    def apply_asset_updates(original: BaseModel, patch: BaseModel) -> BaseModel:
        if isinstance(patch, NewItem):
            if patch.value is not None:
                original.value = patch.value
                original.note = patch.note
            return original

        for field in patch.__fields__:
            patch_value = getattr(patch, field)
            if patch_value is None:
                continue

            original_value = getattr(original, field, None)

            if isinstance(patch_value, BaseModel):
                if isinstance(patch_value, NewItem):
                    if original_value is not None and patch_value.value is not None:
                        original_value.value = patch_value.value
                        original_value.note = patch_value.note
                else:
                    nested_original = original_value or patch_value.__class__()
                    nested = apply_asset_updates(nested_original, patch_value)
                    setattr(original, field, nested)

            elif hasattr(patch_value, "value") and patch_value.value is not None:
                setattr(original, field, patch_value)

        return original

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def generate_response(state: AssetsGraphState) -> dict:
        structured_output = await llm_with_structured.ainvoke(state["messages"])
        return {
            "messages": [structured_output["raw"]],
            "structured_response": structured_output["parsed"],
            "unit_index": 0,
        }

    @retry_on_rate_limit(retries=MAX_RETRIES, default_wait=RETRY_DELAY)
    async def recalculate_node(state: AssetsGraphState) -> dict:

        def format_recalculation_prompt(unit: RecalculationUnit) -> str:
            known_items = [
                item for item in unit.detail_items if item.get("value") is not None
            ]
            missing_items = [
                item for item in unit.detail_items if item.get("value") is None
            ]

            target_item = f"{unit.target_item['item_id'].split('/')[-1]} (value: {unit.target_item['value']})"
            known_detail_items = [
                f"{item['item_id'].split('/')[-1]} (value: {item['value']})"
                for item in known_items
            ]
            missing_items = [
                f"{item['display_name'].split('/')[-1]}" for item in missing_items
            ]

            return RECALCUALTE_SUMMARY_TEMPLATE.format(
                target_item=target_item,
                known_detail_items="\n- ".join(known_detail_items),
                missing_items="\n- ".join(missing_items),
                remaining_value=unit.remain_value(),
            )

        def _set_nested_asset_value(
            asset_obj: Assets, item_id: str, value: float | None, note: str = ""
        ) -> bool:
            fields = item_id.split("/")
            current = asset_obj
            for field in fields[:-1]:
                current = getattr(current, field, None)
                if current is None:
                    logger.warning(f"Intermediate field {field} is None in {item_id}")
                    return False

            last = fields[-1]
            item = getattr(current, last, None)

            if item is None:
                logger.info(f"{item_id} is None → creating NewItem() and updating")
                setattr(current, last, NewItem(value=value, note=note))
                return True

            if isinstance(item, NewItem):
                item.value = value
                item.note = note
                return True

            logger.warning(f"Field {item_id} is not a NewItem and cannot be updated")
            return False

        def _get_object_by_item_id(obj, path: str):
            try:
                return reduce(getattr, path.split("/"), obj)
            except AttributeError:
                return None

        def auto_fill_target_from_details(
            unit: RecalculationUnit, structured_response: Assets
        ) -> bool:

            if unit.get_target_value() != 0:
                return False

            valid_items = [
                item
                for item in unit.detail_items
                if isinstance(item.get("value"), (int, float))
            ]
            if not valid_items:
                return False

            total = sum(item["value"] for item in valid_items)
            combined_note = " + ".join(
                getattr(
                    _get_object_by_item_id(
                        structured_response, item.get("item_id", "")
                    ),
                    "note",
                    item.get("display_name", ""),
                )
                for item in valid_items
                if item.get("value") is not None
            )

            return _set_nested_asset_value(
                structured_response,
                unit.target_item["item_id"],
                value=total,
                note=combined_note or "Auto-filled from detail items",
            )

        def auto_fill_last_detail_from_target(
            unit: RecalculationUnit, structured_response: Assets
        ) -> bool:
            detail_items = unit.detail_items
            detail_sum = sum(
                item["value"]
                for item in detail_items
                if isinstance(item.get("value"), (int, float))
            )

            if round(detail_sum, 2) != 0:
                return False

            target_value = unit.get_target_value()
            if not isinstance(target_value, (int, float)) or target_value == 0:
                return False

            latest_item = max(detail_items, key=lambda x: x.get("no", 0))
            if latest_item and latest_item.get("item_id"):
                return _set_nested_asset_value(
                    structured_response,
                    latest_item["item_id"],
                    value=target_value,
                    note=getattr(
                        _get_object_by_item_id(
                            structured_response, unit.target_item.get("item_id", "")
                        ),
                        "note",
                        unit.target_item.get("display_name", ""),
                    ),
                )
            return False

        def disable_redundant_items(
            unit: RecalculationUnit, structured_response: Assets
        ) -> Assets | None:
            remain = unit.remain_value()
            logger.debug(
                f"Checking unit '{unit.target_item.get('display_name')}' with remain_value = {remain}"
            )

            if remain >= 0:
                logger.debug("Remain value is non-negative; no adjustment needed.")
                return None

            candidates = [
                item
                for item in unit.detail_items
                if item.get("value") is not None
                and isinstance(item["value"], (int, float))
            ]

            logger.debug(
                f"Found {len(candidates)} candidate items to check for adjustment. \n{candidates}"
            )

            for r in range(1, len(candidates) + 1):
                for combo in combinations(candidates, r):
                    total = sum(item["value"] for item in combo)
                    if round(total, 2) == round(abs(remain), 2):
                        field_names = [
                            item.get("display_name", item.get("field_name", "unknown"))
                            for item in combo
                        ]
                        logger.info(
                            f"Matched adjustment: set {field_names} to None "
                            f"to fix remain_value = {remain}"
                        )

                        for item in combo:
                            item_id = item.get("item_id")
                            logger.debug(f"{item_id}")
                            if item_id:
                                _set_nested_asset_value(
                                    structured_response,
                                    item_id,
                                    value=None,
                                    note="Auto-zeroed to match total",
                                )
                        return True

            logger.debug("No matching combination found for auto-zero adjustment.")
            return None

        def selective_update_detail_fields_only(
            original: BaseModel, patch: BaseModel
        ) -> BaseModel:
            for field_name in patch.__fields__:
                if field_name.startswith("total_") and field_name.endswith(
                    "_extracted"
                ):
                    continue  # Skip total field

                patch_value = getattr(patch, field_name)
                if patch_value is None:
                    continue

                original_value = getattr(original, field_name, None)
                if isinstance(patch_value, BaseModel) and hasattr(patch_value, "value"):
                    if (
                        original_value is None
                        or patch_value.value != original_value.value
                        or patch_value.note != original_value.note
                    ):
                        setattr(original, field_name, patch_value)
                elif isinstance(patch_value, BaseModel):
                    nested = original_value or patch_value.__class__()
                    updated = selective_update_detail_fields_only(nested, patch_value)
                    setattr(original, field_name, updated)

            return original

        def apply_asset_detail_patch(
            structured_response: BaseModel,
            path: list[str],
            patch: BaseModel,
            model_class: type,
        ):
            parent = structured_response
            for key in path[:-1]:
                parent = getattr(parent, key)
            field_name = path[-1]
            original = getattr(parent, field_name, None)
            if original is None:
                original = model_class()
            updated = selective_update_detail_fields_only(original, patch)
            setattr(parent, field_name, updated)

        structured_response = state["structured_response"]
        if not structured_response:
            logger.warning("No structured response available")
            return {}

        balance_sheet_items = transform_to_output_item(structured_response)
        template = map_values_into_sheet(
            sheet_id=SheetType.balance_sheet.value,
            item_list=balance_sheet_items,
        )

        units = recalculation_builder(template)
        index = state.get("unit_index", 0)
        logger.debug(f"ASSETS: Current unit index: {index+1} / {len(units)}")

        if index >= len(units):
            logger.info("All units processed.")
            return {"is_evaluated": True}

        unit = units[index]
        sum_detail_items = unit.sum_details()
        remain = unit.remain_value()

        logger.debug(unit.target_item)
        logger.debug(unit.detail_items)
        logger.debug(unit.target_item["item_id"])
        logger.debug(f"sum detail value: {sum_detail_items}")
        logger.debug(f"remain value: {remain}")

        if remain == 0:
            logger.debug(f"Unit {index+1} is already valid. Skipping.")
            return {
                **state,
                "messages": state.get("messages", []),
                "unit_index": index + 1,
                "is_evaluated": index + 1 >= len(units),
            }

        # Case: remain < 0 → try to auto-zero some fields
        if remain < 0 and unit.get_target_value() > 0:
            if disable_redundant_items(unit, structured_response):
                logger.info(f"Auto-zero adjustment applied for unit {index+1}")
                return {
                    **state,
                    "structured_response": structured_response,
                    "messages": state.get("messages", []),
                    "unit_index": index + 1,
                    "is_evaluated": index + 1 >= len(units),
                }

        # Case: remain > 0 → call LLM
        logger.info(f"Unit {index+1} requires LLM recalculation (remain = {remain})")
        detail_item_name = unit.target_item["item_id"].split("/")[-2]
        detail_table_md_str = state["data"]["detail_tables"].get(detail_item_name, "")
        if not detail_table_md_str:
            detail_table_md_str = state["data"].get("balance_sheet", "")
        recalculation_messages = [
            SystemMessage(content=RECALCUALTE_SYSTEM_PROMPT),
            HumanMessage(
                content=RECALCUALTE_HUMAN_PROMPT.format(
                    table_markdown=detail_table_md_str,
                    recalculation_summary=format_recalculation_prompt(unit),
                )
            ),
        ]

        if detail_item_name == "inventories":
            response_format = Inventories
        elif detail_item_name == "current_assets":
            response_format = CurrentAssets
        elif detail_item_name == "tangible_fixed_assets":
            response_format = TangibleFixedAssets
        elif detail_item_name == "intangible_fixed_assets":
            response_format = IntangibleFixedAssets
        elif detail_item_name == "investment_assets":
            response_format = InvestmentAssets

        llm_with_sub_structured = model.with_structured_output(
            response_format,
            include_raw=True,
        )
        recalculation_output = await llm_with_sub_structured.ainvoke(
            recalculation_messages
        )

        new_detail_total = 0.0
        for item in unit.detail_items:
            item_id = item.get("item_id").split("/")[-1]
            item = getattr(recalculation_output["parsed"], item_id)
            value = item.value if item is not None else 0
            if isinstance(value, (int, float)):
                new_detail_total += value

        target_value = unit.get_target_value()
        if abs(new_detail_total - target_value) <= 0.01:
            logger.info(
                f"✅ Total of detail items ({new_detail_total}) matches target ({target_value})"
            )

            path_map = {
                "inventories": (["current_assets", "inventories"], Inventories),
                "current_assets": (["current_assets"], CurrentAssets),
                "tangible_fixed_assets": (
                    ["fixed_assets", "tangible_fixed_assets"],
                    TangibleFixedAssets,
                ),
                "intangible_fixed_assets": (
                    ["fixed_assets", "intangible_fixed_assets"],
                    IntangibleFixedAssets,
                ),
                "investment_assets": (
                    ["fixed_assets", "investment_assets"],
                    InvestmentAssets,
                ),
            }
            path, cls = path_map[detail_item_name]
            apply_asset_detail_patch(
                structured_response, path, recalculation_output["parsed"], cls
            )

            return {
                **state,
                "structured_response": structured_response,
                "messages": state.get("messages", []),
                "unit_index": index + 1,
                "is_evaluated": index + 1 >= len(units),
            }

        else:
            logger.warning(
                f"❌ Mismatch: detail total = {new_detail_total}, target = {target_value}"
            )
            # Fallback: fill detail if all are zero and target exists
            if auto_fill_last_detail_from_target(unit, structured_response):
                return {
                    **state,
                    "structured_response": structured_response,
                    "messages": state.get("messages", []),
                    "unit_index": index + 1,
                    "is_evaluated": index + 1 >= len(units),
                }

        # Fallback: fill target if it is missing but detail has values
        if auto_fill_target_from_details(unit, structured_response):
            return {
                **state,
                "structured_response": structured_response,
                "messages": state.get("messages", []),
                "unit_index": index + 1,
                "is_evaluated": index + 1 >= len(units),
            }

        return {
            **state,
            "structured_response": structured_response,
            "messages": state.get("messages", []),
            "unit_index": index + 1,
            "is_evaluated": index + 1 >= len(units),
        }

    workflow = StateGraph(AssetsGraphState)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("recalculate_node", recalculate_node)
    workflow.add_conditional_edges(
        "generate_response",
        lambda state: "recalculate_node",
        ["recalculate_node"],
    )
    workflow.add_conditional_edges(
        "recalculate_node",
        lambda state: ("recalculate_node" if not state.get("is_evaluated") else END),
        ["recalculate_node", END],
    )
    workflow.set_entry_point("generate_response")

    return workflow.compile()
