from typing import Optional

from fastapi import Header, Request, status
from starlette.exceptions import HTTPException

from app.utils.constants import Message
from app.utils.security import validate_token


def get_current_user(request: Request, token: Optional[str] = Header(None)):
    if "Token" in request.headers:
        token = request.headers["Token"]
    elif "Authorization" in request.headers:
        token = request.headers["Authorization"].split()[-1]

    valid_token, data = validate_token(token)

    if not valid_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=Message.MSG_LOGIN_INVALID_TOKEN_UNAUTHORIZED,
        )
    request.state.email = data["email"]
    request.state.user_id = data["user_id"]
    # request.state.roles = data["roles"]
    request.state.group_names = data["group_names"] if "group_names" in data else []
