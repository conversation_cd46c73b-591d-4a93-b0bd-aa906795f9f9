from typing import Literal

from langchain_openai import AzureChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Send
from loguru import logger

from app.agents.assets import create_assets_agent_v3, get_assets_agent_messages
from app.agents.base import (
    AssetsGraphState,
    Checkpointer,
    FinancialAnalyzerGraphState,
    IncomeGraphState,
    LiabilitiesGraphState,
)
from app.agents.highlights.base import abatch_extract_highlights
from app.agents.highlights.prompt import (
    asset_human_prompt,
    income_human_prompt,
    liabilities_human_prompt,
)
from app.agents.income import get_income_agent_messages
from app.agents.income.graph import create_income_agent_v2
from app.agents.liabilities import (
    create_liabilities_agent_v3,
    get_liabilities_agent_messages,
)
from app.agents.table_mapping.graph import filter_income_statement_tables, map_table
from app.data_processing.data_extraction import FinancialPDFExtractor
from app.schemas.highlights import ExtractHighlightInput
from app.schemas.new_income import IncomeStatement  # noqa
from app.schemas.table_mapping import TableMapping
from app.utils.azure_openai_client import azure_openai_chat_model


class FinancialAnalyzerGraph:
    def __init__(
        self,
        model: AzureChatOpenAI = azure_openai_chat_model,
        checkpointer: Checkpointer | None = None,
    ):
        self.checkpointer = checkpointer
        self.model = model
        self.graph = StateGraph(FinancialAnalyzerGraphState)
        self.app: CompiledStateGraph = self.compile_graph()

    def add_nodes(self):
        self.graph.add_node(extract_fs_node)
        self.graph.add_node("extract_assets_node", extract_assets_node)
        self.graph.add_node("extract_liabilities_node", extract_liabilities_node)
        self.graph.add_node("extract_income_node", extract_income_node)
        self.graph.add_node("post_process", post_process_node)
        self.graph.add_node("map_tables_node", map_tables_node)

    def add_edges(self):
        self.graph.set_entry_point("extract_fs_node")
        self.graph.add_edge(
            "extract_fs_node",
            "map_tables_node",
        )
        self.graph.add_conditional_edges(
            "map_tables_node",
            extract_items,
            [
                END,
                "extract_liabilities_node",
                "extract_income_node",
                "extract_assets_node",
            ],
        )

    def compile_graph(self) -> CompiledStateGraph:
        self.add_nodes()
        self.add_edges()
        return self.graph.compile()


async def extract_fs_node(state: FinancialAnalyzerGraphState) -> dict:
    logger.debug("extract_fs_node: Start extracting financial statement")

    extractor = FinancialPDFExtractor()
    table_list, table_markdown = await extractor.initialize_and_extract_table_markdown(
        state["file_bytes"]
    )
    logger.debug("Finish extracting table markdown")
    return {"data": table_markdown, "table_list": table_list}


async def map_tables_node(state: FinancialAnalyzerGraphState = None) -> dict:
    """Node to map tables to structured output."""
    logger.debug("Start mapping tables")
    bs_markdown_tables, bs_table_ids, detail_table_markdown_by_category = (
        await map_table(
            data=state["data"],
        )
    )

    # Extract the markdown tables for income statement
    ic_markdown_tables, ic_table_ids = await filter_income_statement_tables(
        state["data"]
    )
    logger.debug("Finish mapping tables.")
    table_mapping = TableMapping(
        balance_sheet=bs_markdown_tables,
        income_statement=ic_markdown_tables,
    )

    if not (table_mapping.balance_sheet and table_mapping.income_statement):
        logger.debug("No relevant tables found.")
        return {"map_table_response": ""}

    return {
        "map_table_response": table_mapping,
        "income_table_ids": ic_table_ids,
        "balance_table_ids": bs_table_ids,
        "detail_table_markdown_by_category": detail_table_markdown_by_category,
    }


async def extract_income_node(
    state: IncomeGraphState,
    checkpointer: Checkpointer | None = None,
) -> Literal["post_process"]:
    """Node to extract income from the structured output."""

    # we can use the same agent creation function (update response format)
    agent = create_income_agent_v2()
    # checkpointer=checkpointer,
    # max_retries=state["max_retries"],
    # response_format=IncomeStatement,
    response: IncomeGraphState = await agent.ainvoke(state)
    return Command(
        goto="post_process", update={"income_response": response["structured_response"]}
    )


async def extract_liabilities_node(
    state: LiabilitiesGraphState,
    checkpointer: Checkpointer | None = None,
) -> Literal["post_process"]:
    """Node to extract liabilities from the structured output."""
    logger.info("Starting liabilities extraction...")

    # Initial extraction
    # agent = create_asset_agent(
    #     checkpointer=checkpointer,
    #     max_retries=state["max_retries"],
    #     response_format=LiabilitiesAndNetAssets,
    # )
    # response: SubGraphState = await agent.ainvoke(state)
    # response: SubGraphState = await reflect_liabilities_extraction(response, state)
    agent = create_liabilities_agent_v3()
    response: LiabilitiesGraphState = await agent.ainvoke(state)

    # rule-based extraction for hard cases
    # response = await rule_based_extraction(state["data"], response)

    logger.info("Liabilities extraction and verification completed")

    return Command(
        goto="post_process",
        update={"liabilities_response": response["structured_response"]},
    )


async def extract_assets_node(
    state: AssetsGraphState,
    checkpointer: Checkpointer | None = None,
) -> Literal["post_process"]:
    """Node to extract assets from the structured output."""
    logger.info("Starting assets extraction...")

    agent = create_assets_agent_v3()
    response: AssetsGraphState = await agent.ainvoke(state)

    logger.info("Assets extraction and verification completed")

    return Command(
        goto="post_process",
        update={"asset_response": response["structured_response"]},
    )


async def post_process_node(
    state: FinancialAnalyzerGraphState,
) -> Literal["END"]:
    """Node to post process the extracted data."""

    highlight_inputs: list[ExtractHighlightInput] = []

    if state["asset_response"]:
        highlight_inputs.append(
            ExtractHighlightInput(
                input_instance=state["asset_response"],
                table_list=state["table_list"],
                prompt=asset_human_prompt,
            )
        )

    if state["liabilities_response"]:
        logger.debug(f"Balance sheet table ids: {state['balance_table_ids']}")
        highlight_inputs.append(
            ExtractHighlightInput(
                input_instance=state["liabilities_response"],
                table_list=state["table_list"],
                prompt=liabilities_human_prompt,
            )
        )

    if state["income_response"]:
        # filter out the income statement tables
        logger.debug(f"Income table ids: {state['income_table_ids']}")
        highlight_inputs.append(
            ExtractHighlightInput(
                input_instance=state["income_response"],
                table_list=[state["table_list"][i] for i in state["income_table_ids"]],
                prompt=income_human_prompt,
            )
        )

    asset_response, liabilities_response, income_response = (
        await abatch_extract_highlights(highlight_inputs)
    )

    return Command(
        goto="END",
        update={
            "asset_response": asset_response,
            "liabilities_response": liabilities_response,
            "income_response": income_response,
        },
    )


def extract_items(state: FinancialAnalyzerGraphState):
    """Node to extract items from the structured output."""
    if not state["map_table_response"]:
        return END
    return [
        Send(
            node="extract_liabilities_node",
            arg=LiabilitiesGraphState(
                data=state["map_table_response"].balance_sheet,
                messages=get_liabilities_agent_messages(
                    state["map_table_response"].balance_sheet
                ),
                is_evaluated=False,
                # number_of_tool_calls=0,
                # max_retries=state["max_retries_per_subgraph"],
            ),
        ),
        Send(
            node="extract_income_node",
            arg=IncomeGraphState(
                data=state["map_table_response"].income_statement,
                messages=get_income_agent_messages(
                    state["map_table_response"].income_statement
                ),
                is_evaluated=False,
                is_reflected=False,
                # number_of_tool_calls=0,
                # max_retries=state["max_retries_per_subgraph"],
            ),
        ),
        Send(
            node="extract_assets_node",
            arg=AssetsGraphState(
                data={
                    "balance_sheet": state["map_table_response"].balance_sheet,
                    "detail_tables": state["detail_table_markdown_by_category"],
                },
                messages=get_assets_agent_messages(
                    state["map_table_response"].balance_sheet
                ),
                number_of_tool_calls=0,
                max_retries=state["max_retries_per_subgraph"],
            ),
        ),
    ]
