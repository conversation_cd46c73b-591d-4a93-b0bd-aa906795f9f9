services:
  app:
    container_name: ai-analysis-agent
    image: ai-analysis-agent:latest
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000" # for dev
    volumes:
      - ./.env:/app/.env
      - ./app:/app/app
      - ./logs:/app/logs
      - ./pyproject.toml:/app/pyproject.toml
      - ./static:/app/static
      - ./local_files:/app/local_files
    restart: unless-stopped
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]