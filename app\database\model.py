from datetime import datetime

from pydantic.types import constr
from sqlalchemy import Column, DateTime, event
from sqlalchemy.ext.mutable import Mutable

# SQLAlchemy models
# PrimaryKey = conint(ge=1, lt=2147483647)
NameStr = constr(pattern=r"^(?!\s*$).+", strip_whitespace=True, min_length=1)


class TimeStampMixin(object):

    created_time = Column(
        DateTime(timezone=True), nullable=False, default=datetime.utcnow
    )
    updated_time = Column(
        DateTime(timezone=True), nullable=False, default=datetime.utcnow
    )

    @staticmethod
    def _updated_at(mapper, connection, target):
        target.updated_time = datetime.utcnow()

    @classmethod
    def __declare_last__(cls):
        event.listen(cls, "before_update", cls._updated_at)


class MutableList(Mutable, list):
    def append(self, value):
        list.append(self, value)
        self.changed()

    def remove(self, value):
        rs = list.remove(self, value)
        self.changed()
        return rs

    def __setitem__(self, i: int, o: int):
        list.__setitem__(self, i, o)
        self.changed()

    @classmethod
    def coerce(cls, key, value):
        if not isinstance(value, MutableList):
            if isinstance(value, list):
                return MutableList(value)
            return Mutable.coerce(key, value)
        else:
            return value
