from typing import Any, <PERSON>

from langgraph.checkpoint.base import <PERSON><PERSON>heckpointSaver
from langgraph.graph import MessagesState

from app.data_processing.model import Table
from app.schemas.table_mapping import TableMapping

Checkpointer = Union[None, bool, BaseCheckpointSaver]


class SubGraphState(MessagesState):
    number_of_tool_calls: int = 0
    structured_response: Any = None  # output of the subgraph
    data: str | None = None
    max_retries: int = 2


class FinancialAnalyzerGraphState(MessagesState):
    """State for the Financial Analyzer Graph.

    This state includes the number of tool calls and the structured response.
    """

    file_bytes: str | None = None
    data: str | None = None
    number_of_tool_calls: int = 0
    structured_response: Any = None
    asset_response: Any = None
    income_response: Any = None
    liabilities_response: Any = None
    map_table_response: TableMapping | None = None

    table_list: list[Table] | None = None
    max_retries_per_subgraph: int = 2
    income_table_ids: list[int] | None = None
    balance_table_ids: list[int] | None = None


class IncomeGraphState(MessagesState):
    structured_response: Any = None
    data: str | None = None
    is_evaluated: bool = False


class LiabilitiesGraphState(MessagesState):
    structured_response: Any = None
    data: str | None = None
    is_evaluated: bool = False
    unit_index: int = 0


class AssetsGraphState(MessagesState):
    structured_response: Any = None
    data: str | None = None
    is_evaluated: bool = False
    unit_index: int = 0


def dict_to_markdown_table_simple(data: dict) -> str:
    """
    Convert a dictionary to a simple markdown table format.
    This function takes a dictionary and transforms it into a markdown table with two columns:
    'Item' and 'Mapped Value'.
    Args:
        data (dict): The input dictionary to convert to markdown table format
    Returns:
        str: A markdown-formatted table as a string with each row representing a key-value pair
             from the input dictionary
    """

    lines = ["| Item | Mapped Value |", "|------|--------------------|"]

    def flatten(d, prefix=""):
        for key, value in d.items():
            name = prefix + key.replace("_", " ").title()
            if isinstance(value, dict):
                if "value" in value:
                    if value["value"] is not None:
                        lines.append(f"| {name} | {value['value']:,} |")
                    else:
                        lines.append(f"| {name} | N/A |")
                else:
                    flatten(value, prefix=prefix)
            elif isinstance(value, (int, float)):
                lines.append(f"| {name} | {value:,} |")
            elif value is None:
                # lines.append(f"| {name} | N/A |")
                continue

    flatten(data)
    return "\n".join(lines)
