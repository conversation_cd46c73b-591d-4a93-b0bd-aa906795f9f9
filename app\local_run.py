import os
import requests
import argparse
from loguru import logger
from glob import glob


URL = 'http://127.0.0.1:8000/api/v1/conversations/process_v2'

def parse_args():
    parser = argparse.ArgumentParser(description='Process some files.')
    parser.add_argument('--files', nargs='+', default=None,
                        help='an PDF file to process')
    parser.add_argument('--data_dir', default='C:\\Users\\<USER>\\OneDrive - FPT Corporation\\Documents\\CISD\\ai-analysis-agent\\local_files\\RAW')
    parser.add_argument('--token', default='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YhkuoIi6LJNQTs8qE0gEJWmMltJ20nuLUMv5YSHui4A')
    parser.add_argument('--user_id', default='198')
    parser.add_argument('--mode', type=int, default=0, choices=[0, 1, 2], help='0: process unprocessed files, 1: get results 2:overwrite results')
    parser.add_argument('--output_dir', default='C:\\Users\\<USER>\\OneDrive - FPT Corporation\\Documents\\CISD\\ai-analysis-agent\\local_files\\OUTPUT')
    return parser.parse_args()

def main():
    args = parse_args()

    headers = {
        'accept': 'application/json',
        'token': args.token,
    }
    file_paths= []
    if args.files: # process specific files
        for file_name in args.files:
            filepath = os.path.join(args.data_dir, file_name)
            if not os.path.exists(filepath):
                raise Exception(f"File {filepath} does not exist")
            else:
                file_paths.append(filepath)
    else: # process all files in the directory
        for file_name in os.listdir(args.data_dir):
            filepath = os.path.join(args.data_dir, file_name)
            if os.path.isfile(filepath):
                file_paths.append(filepath)
    upload_files = []
    for filepath in file_paths:
        files = {'upload_files': (filepath, open(filepath, 'rb'), 'application/pdf')}
        upload_files.append(files)
    if args.mode == 2: # overwrite results  
        for files in upload_files:
            response = requests.post(url=URL, headers=headers, params={'user_id':args.user_id,}, files=files)
            logger.info(f"{response.json()[0]['id']}\n{response.json()[0]['conversation_name']}\n {response.json()[0]['status']}")
    else: # get results or process unprocessed files
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir)
        success_conversations = None
        
        try:
            url = URL.replace("process_v2", "process")
            response = requests.get(url=url, headers=headers, params={'conv_status': 'SUCCESS'})
            success_conversations = []
            for conversation in response.json():
                conversation_id = conversation['conversation_id']
                conversation_name = conversation['conversation_name']
                success_conversations.append({conversation_id: conversation_name})
        except Exception as e:
            logger.error(f"Error getting result for id: {e}")
        
        if args.mode == 1: # get results
            if success_conversations:
                for conversation in success_conversations:
                    conversation_id = list(conversation.keys())[0]
                    conversation_name = conversation[conversation_id]
                    url = URL.replace("process_v2", str(conversation_id))
                    response = requests.get(url=url, headers=headers, params={'user_id':args.user_id,})
                    filename = f"{conversation_name}_{conversation_id}.json"
                    logger.info(f"Saving {filename}")
                    with open(os.path.join(args.output_dir, filename), 'w') as f:
                        f.write(response.text)

        if args.mode == 0: # get results                
            import asyncio
            async def process_file(files):
                response = requests.post(url=URL, headers=headers, params={'user_id':args.user_id,}, files=files)
                return response
            all_files = glob(f"{args.data_dir}\\*.pdf")
            all_files = [os.path.basename(f) for f in all_files]
            files_to_process = all_files
            if success_conversations:
                success_docs = [c.values() for c in success_conversations]
                files_to_process = [f for f in all_files if f not in success_docs]
            logger.info(f"Processing {files_to_process}")
            for filename in files_to_process:
                filepath = os.path.join(args.data_dir, filename)
                files = {'upload_files': (filepath, open(filepath, 'rb'), 'application/pdf')}
                # response = requests.post(url=URL, headers=headers, params={'user_id':args.user_id,}, files=files)
                # wait for the process to finish with async await

                response = asyncio.run(process_file(files))
                logger.info(f"{response.json()[0]['id']}\n{response.json()[0]['conversation_name']}\n {response.json()[0]['status']}")


if __name__=='__main__':
    main()