# from sqlalchemy_searchable import make_searchable, search
import logging
import math
from collections import namedtuple
from typing import Iterable, List, Optional, TypeVar

import sqlalchemy
from fastapi import Depends, HTTPException, Query, status
from pydantic.types import <PERSON><PERSON>, constr
from six import string_types
from sqlalchemy import and_, create_engine, inspect, not_, or_, orm
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, joinedload, load_only, sessionmaker
from sqlalchemy.orm.base import InspectionAttr
from sqlalchemy.orm.relationships import RelationshipProperty
from sqlalchemy_filters import apply_filters, apply_pagination, apply_sort
from sqlalchemy_filters.exceptions import BadFilterFormat
from starlette.requests import Request

from app.settings import Database
from app.utils.constants import Message

log = logging.getLogger(__name__)

engine = create_engine(
    str(Database.postgres_db_url),
    pool_size=Database.pool_size,
    max_overflow=Database.max_overflow,
    pool_pre_ping=True,
)

SessionLocal = sessionmaker(bind=engine)


Base = declarative_base()

ModelType = TypeVar("ModelType", bound=Base)  # type: ignore


# allows only printable characters
QueryStr = constr(pattern=r"^[ -~]+$", min_length=1)

BooleanFunction = namedtuple(
    "BooleanFunction", ("key", "sqlalchemy_fn", "only_one_arg")
)
BOOLEAN_FUNCTIONS = [
    BooleanFunction("or", or_, False),
    BooleanFunction("and", and_, False),
    BooleanFunction("not", not_, True),
]


def get_db(request: Request):
    return request.state.db


def get_class_by_tablename(table_fullname: str):
    for c in Base.registry._class_registry.values():
        if hasattr(c, "__table__"):
            if c.__table__.fullname.lower() == table_fullname.lower():
                return c

    raise Exception(f"Incorrect tablename {table_fullname}")


def get_last_updated(db_session: Session, table, custom_field):
    return db_session.query(table).order_by(custom_field.desc()).first()


def is_existed_data(session: Session, table) -> bool:
    return session.query(table).first() is not None


def get_by_id(db_session: Session, table, id, join_fields=[]):
    query = db_session.query(table).filter(table.id == id)
    if join_fields:
        query = perform_join(table, query, join_fields=join_fields)
    return query.first()


def get_by_ids(db_session: Session, table, ids):
    return db_session.query(table).filter(table.id.in_(ids)).all()


def get_by_list_values(db_session: Session, table, field, values):
    return db_session.query(table).filter(field.in_(values)).all()


def get_by_custom_field(
    db_session: Session, table, custom_field, value, fetch_all=False
):
    if not fetch_all:
        return db_session.query(table).filter(custom_field == value).first()
    else:
        return db_session.query(table).filter(custom_field == value).all()


def get_by_custom_ilike(db_session: Session, table, custom_field, value):
    return db_session.query(table).filter(custom_field.ilike(value)).first()


def get_by_filter(db_session, table, filters: List = [], orders=[]):
    """Fetches a query object based on the model class name."""
    query = db_session.query(table).filter(*filters)
    if orders:
        query = query.order_by(*orders)
    return query.all()


def insert_row(db_session, obj_table):
    """
    insert table common
    """
    # try:
    db_session.add(obj_table)
    db_session.commit()
    db_session.refresh(obj_table)
    return obj_table
    # except Exception as e:
    #     log.error(e)
    #     raise HTTPException(
    #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
    #     )


def insert_multi_rows(db_session: Session, obj_tables: List) -> List:
    """
    Insert multiple rows into the database.
    """
    try:
        db_session.add_all(obj_tables)
        db_session.commit()
        for obj in obj_tables:
            db_session.refresh(obj)
        return obj_tables
    except Exception as e:
        log.error(f"Error inserting multiple rows: {e}")
        db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


def update_row(db_session, obj_table, obj_table_in):
    """
    update table common
    """
    # try:
    update_data = obj_table_in.dict(exclude_none=True)

    for field in update_data:
        setattr(obj_table, field, update_data[field])
    db_session.commit()
    db_session.refresh(obj_table)
    return obj_table
    # except Exception as e:
    #     log.error(e)
    #     raise HTTPException(
    #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
    #     )


def delete_row(db_session, obj_table):
    """
    delete rows in table common
    """
    # try:
    db_session.delete(obj_table)
    db_session.commit()
    return {"message": Message.DELETED_SUCCESSFULLY}
    # except Exception as e:
    #     log.error(e)
    #     raise HTTPException(
    #         status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
    #     )


def delete_multi_rows(db_session: Session, table, custom_field, value):
    db_session.query(table).filter(custom_field == value).delete()
    db_session.commit()
    return {"message": Message.DELETED_SUCCESSFULLY}


# Build Search, sort and filter common
def common_parameters(
    db_session: orm.Session = Depends(get_db),
    page: int = Query(1, gt=0, lt=2147483647),
    items_per_page: int = Query(10, alias="itemsPerPage", gt=-2, lt=2147483647),
    filter_spec: Json = Query([], alias="filterBy"),
    sort_by: List[str] = Query([], alias="sortBy"),
    descending: List[bool] = Query([], alias="descending"),
    query_str: QueryStr = Query("", alias="q"),  # type: ignore
    join_attrs: List[str] = Query([], alias="join"),
    search_fields: List[str] = Query([], alias="queryFields"),
):
    return {
        "db_session": db_session,
        "page": page,
        "items_per_page": items_per_page,
        "query_str": query_str,
        "filter_spec": filter_spec,
        "sort_by": sort_by,
        "descending": descending,
        "join_attrs": join_attrs,
        "search_fields": search_fields,
    }


def get_relationship_fields(model):
    mapper = inspect(model)
    relationship_fields = {}
    for prop in mapper.attrs:
        if isinstance(prop, RelationshipProperty):
            relationship_fields[prop.key] = prop
            # relationship_fields.append((prop.key, prop))
    return relationship_fields


def perform_join(model_type: ModelType, query, join_fields=[]):
    for join_field in join_fields:
        join_field = join_field.strip()
        if join_field:
            if join_field in get_relationship_fields(model_type):
                query = query.options(joinedload(getattr(model_type, join_field)))
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=Message.MSG_INVALID_REQUEST,
                )

    return query


def filter_valid_fields(filter_spec, model):
    """
    Filters and validates fields in the filter specification based on the provided model,
    ensuring the model key is included in each filter.

    Args:
        filter_spec (List[dict]): The filter specifications to validate.
        model (Base): The SQLAlchemy model class.

    Returns:
        List[dict]: The filtered and validated filter specification with models included.
    """

    def process_filter(item, current_model):
        # Get valid columns for the current model
        valid_columns = {column.name for column in current_model.__table__.columns}

        # Handle logical structures (and, or)
        if isinstance(item, dict):
            if "or" in item:
                filtered_or = [
                    process_filter(f, current_model)
                    for f in item["or"]
                    if process_filter(f, current_model)
                ]
                if filtered_or:
                    return {"or": filtered_or}

            if "and" in item:
                filtered_and = [
                    process_filter(f, current_model)
                    for f in item["and"]
                    if process_filter(f, current_model)
                ]
                if filtered_and:
                    return {"and": filtered_and}

            # Handle individual filters with "field"
            if "field" in item and item["field"] in valid_columns:
                # Add the model key to the filter
                return {**item, "model": current_model.__tablename__}

        # If the item is invalid, return None
        return None

    # Process the list of filters and remove invalid entries
    return [process_filter(f, model) for f in filter_spec if process_filter(f, model)]


def generate_ilike_filters(model_cls, query_str, search_fields=None):
    partial_terms = [f"%{term}%" for term in query_str.split()]
    # Only include TEXT and VARCHAR columns
    searchable_columns = [
        column_name
        for column_name, column in model_cls.__table__.columns.items()
        if not isinstance(column.type, sqlalchemy.Enum)
        and isinstance(column.type, (sqlalchemy.String, sqlalchemy.Text))
    ]
    # Limit searchable columns to those in search_fields if provided
    if search_fields:
        searchable_columns = [
            column_name
            for column_name in searchable_columns
            if column_name in search_fields
        ]

    ilike_filters = []
    for column_name in searchable_columns:
        column_attr = getattr(model_cls, column_name)

        # For TEXT/VARCHAR columns, use ILIKE directly
        ilike_filters.extend([column_attr.ilike(term) for term in partial_terms])

    return or_(*ilike_filters)


def search_filter_sort_paginate(
    db_session: Session,
    model,
    query_str: Optional[str] = None,
    filter_spec: Optional[List[dict]] = None,
    page: int = 1,
    items_per_page: int = 5,
    sort_by: Optional[List[str]] = None,
    descending: Optional[List[bool]] = None,
    join_attrs: Optional[List[str]] = None,
    query=None,
    search_fields: Optional[List[str]] = None,
    expected_fields: Optional[List[str]] = None,
):
    """
    Common functionality for searching, filtering, sorting, and pagination.

    Args:
        db_session (Session): The SQLAlchemy session.
        model (Base): The SQLAlchemy model class.
        query_str (str, optional): Query string for search functionality.
        filter_spec (List[dict], optional): Filter specifications.
        page (int): Page number for pagination.
        items_per_page (int): Items per page for pagination.
        sort_by (List[str], optional): Fields to sort by.
        descending (List[bool], optional): Sorting order.
        join_attrs (List[str], optional): Attributes to join.
        query: Pre-existing query.
        search_fields (List[str], optional): Fields to search in.
        expected_fields (List[str], optional): Fields to return in the response.

    Returns:
        dict: Paginated, filtered, and sorted query results.
    """
    model_cls = get_class_by_tablename(model)
    query = query or db_session.query(model_cls)

    if query_str:
        ilike_filters = generate_ilike_filters(model_cls, query_str, search_fields)
        query = query.filter(ilike_filters)

    # Validate filter_spec and sort_by fields
    if filter_spec:
        filter_spec = filter_valid_fields(filter_spec, model_cls)

    valid_columns = {column.name for column in model_cls.__table__.columns}
    if sort_by:
        valid_sort_indices = [
            index for index, field in enumerate(sort_by) if field in valid_columns
        ]
        sort_by = [sort_by[i] for i in valid_sort_indices]
        descending = [descending[i] for i in valid_sort_indices]

    # Apply joins if needed
    query = join_required_attrs(query, model_cls, join_attrs)
    query = perform_join(model_cls, query, join_fields=join_attrs)

    # Apply filters
    if filter_spec:
        filter_spec = build_filters_boolean(model, filter_spec)
        query = apply_filters(query, filter_spec)

    # Apply sorting
    if sort_by:
        sort_spec = create_sort_spec(model, sort_by, descending)
        query = apply_sort(query, sort_spec)

    # Select only expected fields (handling relationships separately)
    if expected_fields:
        column_fields = []
        relationship_fields = []

        for field in expected_fields:
            if hasattr(model_cls, field):  # Ensure field exists
                attr = getattr(model_cls, field)

                # Check if it's a column or a relationship
                if isinstance(attr, InspectionAttr) and hasattr(attr, "property"):
                    if hasattr(attr.property, "columns"):  # Regular column
                        column_fields.append(field)
                    else:  # Relationship field (e.g., roles)
                        relationship_fields.append(field)

        # Apply `load_only()` for regular columns (avoids removing entity context)
        if column_fields:
            query = query.options(
                load_only(*[getattr(model_cls, field) for field in column_fields])
            )

        # Apply `joinedload()` for relationship fields
        for rel_field in relationship_fields:
            query = query.options(joinedload(getattr(model_cls, rel_field)))

    # Handle pagination
    if items_per_page < 0:
        items_per_page = None

    query, pagination = apply_pagination(
        query, page_number=page, page_size=items_per_page
    )

    total_page = 1
    if items_per_page:
        total_page = math.ceil(pagination.total_results / items_per_page)
    next_page = page + 1 if page < total_page else -1
    has_more = next_page != -1
    data = query.all()

    return {
        "object": model,
        "total_item": pagination.total_results,
        "total_page": total_page,
        "has_more": has_more,
        "next_page": next_page,
        "data": data,
    }


def join_required_attrs(query, model, join_attrs):
    if not join_attrs:
        return query

    for attr in join_attrs:
        query = query.join(getattr(model, attr), isouter=True)

    return query


def create_filter_spec(model, fields, ops, values):
    """Creates a filter spec."""
    filters = []
    if fields and ops and values:
        for field, op, value in zip(fields, ops, values):
            filters.append({"model": model, "field": field, "op": op, "value": value})

    return filters


def create_sort_spec(model, sort_by, descending):
    """Creates sort_spec."""
    sort_spec = []
    if sort_by and descending:
        for field, direction in zip(sort_by, descending):
            direction = "desc" if direction else "asc"
            sort_spec.append({"model": model, "field": field, "direction": direction})

    return sort_spec


def build_filters_boolean(model, filter_spec):
    """Builds a filter spec."""

    for item in filter_spec:
        if isinstance(item, dict):
            # Check if filter spec defines a boolean function.
            is_bool = False
            for boolean_function in BOOLEAN_FUNCTIONS:
                if boolean_function.key in item:
                    is_bool = True
                    # The filter spec is for a boolean-function
                    # Get the function argument definitions and validate
                    fn_args = item[boolean_function.key]

                    if not _is_iterable_filter(fn_args):
                        raise BadFilterFormat(
                            "`{}` value must be an iterable across the function "
                            "arguments".format(boolean_function.key)
                        )

                    # Add model to the filter spec
                    for fn_arg in fn_args:
                        fn_arg["model"] = model
                    item[boolean_function.key] = fn_args
            if not is_bool:
                # The filter spec is for a normal filter
                # Add model to the filter spec
                item["model"] = model
    return filter_spec


def _is_iterable_filter(filter_spec):
    """`filter_spec` may be a list of nested filter specs, or a dict."""
    return isinstance(filter_spec, Iterable) and not isinstance(
        filter_spec, (string_types, dict)
    )
