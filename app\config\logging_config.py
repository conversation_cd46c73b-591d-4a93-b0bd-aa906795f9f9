import base64
import datetime
import hashlib
import hmac
import json
import sys
import threading
from urllib.parse import parse_qs

import requests
from fastapi import Request
from fastapi.encoders import jsonable_encoder
from loguru import logger

from app.settings import LogAnalytics


def setup_logging(log_file="logs/app.log"):
    logger.remove()
    logger.add(
        log_file,
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{line} | {message}",
        rotation="5 MB",
        retention=10,
    )
    logger_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    logger.add(
        sys.stdout,
        level="INFO",
        colorize=True,
        format=logger_format,
    )


# Build the API signature
def build_signature(
    customer_id, shared_key, date, content_length, method, content_type, resource
):
    x_headers = "x-ms-date:" + date
    string_to_hash = (
        method
        + "\n"
        + str(content_length)
        + "\n"
        + content_type
        + "\n"
        + x_headers
        + "\n"
        + resource
    )
    bytes_to_hash = bytes(string_to_hash, encoding="utf-8")
    decoded_key = base64.b64decode(shared_key)
    encoded_hash = base64.b64encode(
        hmac.new(decoded_key, bytes_to_hash, digestmod=hashlib.sha256).digest()
    ).decode()
    authorization = "SharedKey {}:{}".format(customer_id, encoded_hash)
    return authorization


# Build and send a request to the POST API
def send_log_analytic(body):
    log_analytics = LogAnalytics()
    customer_id = log_analytics.workspace_id
    shared_key = log_analytics.shared_key
    log_type = log_analytics.custom_table
    if not shared_key:
        return ""
    method = "POST"
    content_type = "application/json"
    resource = "/api/logs"
    rfc1123date = datetime.datetime.utcnow().strftime("%a, %d %b %Y %H:%M:%S GMT")
    content_length = len(body)
    signature = build_signature(
        customer_id,
        shared_key,
        rfc1123date,
        content_length,
        method,
        content_type,
        resource,
    )
    uri = (
        "https://"
        + customer_id
        + ".ods.opinsights.azure.com"
        + resource
        + "?api-version=2016-04-01"
    )
    headers = {
        "content-type": content_type,
        "Authorization": signature,
        "Log-Type": log_type,
        "x-ms-date": rfc1123date,
    }
    response = requests.post(url=uri, headers=headers, data=body, timeout=10)
    return response


async def log_request_info(request: Request):
    request_body = {}
    try:
        request_body = await request.json()
    except Exception:
        request_body = {}
    body = jsonable_encoder(request_body)
    # Extract additional request info
    query_param = parse_qs(str(request.query_params))
    logger.info(
        f"{request.method} Path {request.url.path} Headers: {request.headers} Body: {body} Path Params: {request.path_params} Query Params: {query_param} Cookies: {request.cookies}"
    )
    user_id = request.state.user_id if hasattr(request.state, "user_id") else None
    log_analytic = [
        {
            "service": "CISD BP Analysis Agent",
            "url": request.url._url,
            "path": request.url.path,
            "method": request.method,
            "headers": str(jsonable_encoder(request.headers)),
            "body": str(body),
            "query": query_param,
            "cookies": str(request.cookies),
            "user_id": user_id,
        }
    ]
    thread = threading.Thread(
        target=send_log_analytic,
        args=(json.dumps(log_analytic, ensure_ascii=False),),
        daemon=True,  # Ensure the thread stops when the program exits
    )
    thread.start()  # Start the thread to execute
