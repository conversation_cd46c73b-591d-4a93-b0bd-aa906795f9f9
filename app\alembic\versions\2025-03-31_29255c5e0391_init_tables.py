"""init tables

Revision ID: 29255c5e0391
Revises: 
Create Date: 2025-03-31 16:25:47.091883

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "29255c5e0391"
down_revision = "c2e2f6def6f5"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "FinancialDocument",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("filename", sa.String(), nullable=False),
        sa.Column("blob_path", sa.String(), nullable=True),
        sa.Column("pdf_metadata", sa.String(), nullable=True),
        sa.Column("deleted", sa.<PERSON>(), nullable=True),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_FinancialDocument_id"), "FinancialDocument", ["id"], unique=False
    )
    op.create_table(
        "FinancialReport",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("document_id", sa.Integer(), nullable=False),
        sa.Column("filename", sa.String(), nullable=False),
        sa.Column("blob_path", sa.String(), nullable=True),
        sa.Column("deleted", sa.Boolean(), nullable=True),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["FinancialDocument.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_FinancialReport_id"), "FinancialReport", ["id"], unique=False
    )
    op.create_table(
        "FinancialConversation",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("conversation_name", sa.String(), nullable=False),
        sa.Column("conversation_type", sa.String(), nullable=True),
        sa.Column("document_id", sa.Integer(), nullable=False),
        sa.Column("report_id", sa.Integer(), nullable=False),
        sa.Column("mapping_metadata", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=True),
        sa.Column("deleted", sa.Boolean(), nullable=True),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["FinancialDocument.id"],
        ),
        sa.ForeignKeyConstraint(
            ["report_id"],
            ["FinancialReport.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_FinancialConversation_id"),
        "FinancialConversation",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_FinancialConversation_user_id"),
        "FinancialConversation",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_FinancialConversation_user_id"), table_name="FinancialConversation"
    )
    op.drop_index(
        op.f("ix_FinancialConversation_id"), table_name="FinancialConversation"
    )
    op.drop_table("FinancialConversation")
    op.drop_index(op.f("ix_FinancialReport_id"), table_name="FinancialReport")
    op.drop_table("FinancialReport")
    op.drop_index(op.f("ix_FinancialDocument_id"), table_name="FinancialDocument")
    op.drop_table("FinancialDocument")
    # ### end Alembic commands ###
