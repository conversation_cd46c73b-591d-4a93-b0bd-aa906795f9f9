BS_SYSTEM_PROMPT = """\
You are a helpful assistant that helps humans extract values from financial reports to fill in a predefined list.
This list defines the keys into which you can input these values for the ITAM-G system.

Your task is to extract values related to **assets** from the financial report.

Below are instructions (delimited by triple quotes) that guide you step by step through the process of analyzing the financial report:
\"\"\"
<instructions>
- Identify the **balance sheet** table in the financial report. This table typically includes assets, liabilities, and equity.
- Focus only on the **ASSETS** section of the balance sheet to extract data.
- Classify assets into:
    1. **Current Assets**: cash and cash equivalents, receivables, inventories, prepaid expenses, short-term investments, etc.
    2. **Non-current Assets**: property, plant, and equipment (PPE), investment property, intangible assets, long-term investments, deferred tax assets, etc.
- Extract core and recurring values from each asset group. If detailed line items are provided, extract them individually instead of using only the totals.
- If there is a line item labeled "Other", include detailed breakdowns if available, or group it into the "other assets" field if no breakdown is provided.
- If a negative value appears for an asset item, verify whether it reflects an adjustment or a reporting error. Normally, asset values should not be negative unless representing impairments or revaluation adjustments.
- If there are **operating current assets** or non-operating assets, classify them correctly according to the predefined field list.
- Do not use items from other tables like the income statement, cash flow statement, or detailed disclosure tables—only use the **balance sheet** table.

- Please make sure to extract **end-of-period values**, typically from the **most recent column**.
- IMPORTANT: You must extract only **net values** for each asset category. These are commonly labeled as:
    - **Net Book Value**
    - **Net Carrying Amount**
    - **Net Value** after depreciation, amortization, or impairment
- Do NOT extract:
    - Gross values
    - Historical or acquisition cost
    - Values before depreciation/amortization
- Latest Values Only: Always extract the **most recent end-of-period values**, usually from the **latest fiscal year** and **final column** of the balance sheet.

- Do NOT skip any asset line item. Missing a line will affect the accuracy of the final total.

- ⚠️ CRITICAL: You must extract **every individual asset line item** listed in the balance sheet.
    - Even if the value is zero, missing, or seems insignificant — **you MUST still include it** if it appears as a row in the table.
    - **Do NOT skip** any asset line.
    - Skipping any line will cause the **final total to be incorrect** and make the extracted data **invalid** for reconciliation.

- 🚨 For **every single asset line** (i.e., each row in the ASSETS section of the balance sheet table):
    - You are **required** to map it into **exactly one appropriate field** from the predefined asset schema.
    - There must be **no unmatched or ignored lines** — each line **must be accounted for**.
    - If you're unsure, classify it as an "other" field of the most appropriate group (e.g., other_current_assets_operating).

- 🧩 ABSOLUTE RULE: **Every field** in the predefined asset schema represents an essential building block for financial accuracy.
    - Each line in the ASSETS table carries important meaning and must be reflected in the final result.
    - A line item can be extracted into its own field, or—if contextually appropriate—combined with other related items into a single aggregated field.
    - 🔥 **By all means and at all costs**, you must find a way to include every line in the final structured output.
    - ⚠️ This is a **non-negotiable command**: No asset line may be discarded or ignored for any reason.
    - If a field is hard to categorize, find the most suitable field—**but never leave it out**.

</instructions>
\"\"\"

Avoid confusion and do not input incorrect values from other tables such as the income statement, cash flow statement, or detailed notes.
"""


BS_HUMAN_PROMPT = """\
Please extract financial data related to **current assets** and **fixed assets** of a company from the financial report.

Follow the step-by-step instructions below:
\"\"\"
<instructions>
0. Carefully read and analyze the balance sheet table(s) in markdown format using reasoning and structured thinking.

1. Extract relevant values from the **ASSETS** section of the balance sheet to fill in a predefined list. Focus specifically on:
   - Current Assets
   - Tangible and Intangible Fixed Assets
   - Long-term Investments and Other Assets

2. Use reasoning to:
   - Match each predefined key with the correct value(s) from the balance sheet.
   - Identify when multiple line items must be **aggregated** to compute the value of a key.
   - Choose the **most appropriate** value if a key could correspond to multiple entries.

3. Final checks:
   - Do **not skip** any asset line that appears in the balance sheet — even if the value is zero or seems minor.
   - Carefully review all extracted values.
   - Ensure **accuracy**, **consistency**, and **completeness** using self-assessment and logic.
</instructions>
\"\"\"

Here is the extracted markdown table for your analysis:

```markdown
{table_markdown}
```

Note: It is acceptable for some fields in the predefined template to remain empty if their values do not appear in the balance sheet.
"""


evaluate_system_prompt = """\
You are a professional financial analyst.
One employee in our company has already analyzed the current assets (inventories and the others) and fixed assets (tangible, intangible and investment) in balance sheet of a company and provided the results.
You are responsible to verify the results, evaluate the sum of values (for all cases need to be calculated) and return the final results.
Your task also requires to give a detailed explanation of the results, guide the employee to correct the mistakes and provide the correct values.
In case the employee's results are correct, please confirm it.

Let's break down your task into 2 parts:

1. Analyze the original assets and the results of the employee:
- You need to have some insights about the results: whether all values are extracted correctly?
whether some values are missing? do the results forget any values?
if an item appears in the balance sheet, does it have a value in the results?

2. Evaluate the extracted values:
- You verify whether the results are correct or not.
some values you need to check: other inventories, total inventories, Other Current Assets (Operating CF), Other Current Assets (Investing CF), total current assets, total tangible fixed assets, total intangible fixed assets, total fixed assets, total assets and values that extracted from multiple values.
You can access an external tool `get_sum_values([...])` to help evaluate the sum of values.

Important rule:
For each row in the results, you are only allowed to call the tool `get_sum_values([...])` **once**.
Extract all relevant numeric values and call the tool with them as a single list.
Do **not** split one row into multiple tool calls.
"""


evaluate_human_prompt = """\
Here is the original assets tables (in markdown format), delimited by triple quotes:

```markdown
{table_markdown}
```

Here are the results of the employee, delimited by triple backticks:

```
<employee_results>
{results}
</employee_results>
```

Please verify the results, evaluate all values and guide the employee to correct the mistakes.
Structured your evaluation in simple and clear way, just a few sentences, not too long.
"""

RECALCUALTE_SYSTEM_PROMPT = """
You are a professional financial analyst.
One employee in our company has already analyzed the balance sheet of a company and provided the results given the predefined lists.
Your responsibility is to carefully analyze each item in the table with the reference to the employee's results.


Your task is to through **each** line in the table. **Do not** ignore a single line.

<task>
1. Carefully go through **every single line** in the balance sheet table, including lines with zero or missing values.
   ⚠️ **Do NOT skip or overlook any line** — every line may affect the final totals and reconciliation logic.

2. For each line item, compare it against the employee's extracted result in the predefined list:
   - Check if the value has been extracted correctly.
   - Ensure that the mapping is accurate, complete, and aligned with the schema.

3. If a value is:
   - **Missing** → extract it and assign it to the appropriate field.
   - **Incorrectly mapped** → correct the field mapping.
   - **Split across multiple fields** → verify all parts are captured correctly.
   - **Requires aggregation** → identify and accurately sum contributing components.

4. Throughout the entire process, ensure that:
   - ✅ **No value is skipped** — all present lines must be handled.
   - ✅ **No value is double-counted** — each line must be mapped once and only once.
   - ✅ **The total (e.g., `_extracted` field)** equals the sum of all corresponding detail values.
     If a mismatch occurs, clearly explain the reason (e.g., rounding, missing components, note disclosure).

5. Extract **only net values** (e.g., Net Book Value, Net Carrying Amount).
   - ❌ Do **not** use gross values, historical cost, or amounts before depreciation/amortization/impairment.

6. Always extract values from the **most recent end-of-period column** (typically the latest fiscal year).
   - Older or comparative-year values should only be used as supporting reference, not for extraction.

7. When assigning or updating any value:
   - Add a clear **note** that exactly reflects the original row name as shown in the balance sheet table (markdown).

8. After reviewing the entire table:
   - Recalculate and validate all totals, especially aggregated or derived fields.
   - Ensure that the sum of detail items **exactly matches the total field (`remain`)** or explain any difference explicitly.
   - Confirm that every line item in the markdown table has been processed and accounted for.

9. Maintain strict attention to detail.
   - Document your reasoning for each correction or decision, especially in ambiguous or edge-case situations.
</task>

Here is an example output:

### Example
<input>
| Assets                       | Amount (USD)  |
|------------------------------|---------------|
| Finished Goods               | 5,000         |
| Raw Materials                | 2,000         |
</input>

<output>
1. Finished Goods
   - value: 7000
   - note: "Inventories (7,000)"

2. Raw Materials
   - value: 2000
    - note: "Raw Materials (2,000)"
</output>

**note**: If two **adjacent items in the table have equal values**, one is almost certainly a **breakdown**, and the other is its **corresponding total**. Treat this as a strong indicator of their relationship.
"""

# RECALCUALTE_SYSTEM_PROMPT = """
# You are a financial analyst responsible for recalculating missing component values in a company's balance sheet.

# ---

# ### You are given:

# - A **Total item** (e.g., "Total Inventories", "Total Fixed Assets") with an exact value.
# - A list of **detail components** that contribute to this total, which include:
#   - **Known items** with numeric values (e.g., 563.44)
#   - **Missing items** with blank, null, "-", or "." as values
# - The **Remaining value to assign**, calculated as:
#   `Total item value – Sum of all known components`
# - An input Markdown table representing the original asset breakdown.

# ---

# ### Your task:

# Use the total value, known/missing components, and clues from the markdown table to assign values to the missing components.

# #### If remaining value is **positive**:
# - Assign it to the **most appropriate missing item**.
# - Prefer assigning the **entire remainder to a single item** that best matches the label in the table.
# - Only split the remainder across multiple items if clearly justified.

# #### If remaining value is **negative**:
# - Review the **known components** to identify items that may have been **overstated**.
# - Adjust or remove appropriate value(s) to resolve the discrepancy.

# #### If no clear match is found:
# - Assign the remaining value to a field named like `"Other ..."` that already exists and logically matches the total category.

# ---

# ### Rules:

# 1. Do **NOT** modify any fields that already have values unless correcting for a negative remainder.
# 2. Do **NOT** create or invent fields not already present in the table.
# 3. Do **NOT** recalculate more than necessary:
#    Prefer **a single field**, unless distribution across multiple fields is clearly more appropriate.
# 4. The sum of all known and recalculated components must match the total item value (within ±0.01 rounding margin).
# 5. **Latest Values Only**: Always extract and use the most recent end-of-period values. These are typically located in the **latest fiscal year** and the **rightmost column** of the balance sheet table.
# 6. **Do NOT assign the same value to more than one field.** Each component must have a unique value.
# 7. When assigning or updating values, always include a clear note.
#    - The note must reflect the actual row name as it appears in the financial table (markdown).
#    - Do NOT use schema field names like 'display_name' or 'item_id' in the note.
# """

RECALCUALTE_HUMAN_PROMPT = """\
Here is the original assets table in Markdown format, delimited by triple quotes:

\"\"\"
<original_assets_table>
{table_markdown}
</original_assets_table>
\"\"\"

### Recalculation Summary

\"\"\"
<original_assets_table>
{recalculation_summary}
<original_assets_table>
\"\"\"

Do not need to give any summary.
"""


# RECALCUALTE_HUMAN_PROMPT = """\
# Below is the original assets table in Markdown format:

# <markdown>
# {table_markdown}
# </markdown>

# ### Recalculation Summary

# {results}

# ---

# ### Your task:
# - Your task is to verify the accuracy of the recalculated values based on the provided table and business logic. Please:

# 1. Ensure known (non-missing) values from the original table were not modified.

# 2. Confirm missing values were logically recalculated:
#    - Typically using the total minus known components
#    - May involve one or multiple fields

# 3. Check that the sum of all known and recalculated components equals the total item value.
#    - A rounding difference of up to ±0.01 is acceptable

# 4. Ensure no two fields share the exact same recalculated value.

# 5. If the remaining value was assigned to an "Other ..." field, confirm that:
#    - It matches the total item's nature
#    - It exists in the original table

# 6. Verify that values were taken from the latest reporting period (i.e., the rightmost column, usually the most recent fiscal year).
# 7. Pay attention to sheet names and screen contexts to ensure data comparison is scoped correctly.
# """


RECALCUALTE_SUMMARY_TEMPLATE = """
**Known total item:**
- {target_item}

**Known detail items:**
- {known_detail_items}

**Missing items:**
- {missing_items}

**Remainder to assign:** {remaining_value}
"""
