from app.agents.base import FinancialAnalyzerGraphState
from app.agents.graph import FinancialAnalyzerGraph
from app.schemas.base import SheetType
from app.schemas.highlights import OutputItem
from app.utils.azure_openai_client import azure_openai_chat_model, langfuse_handler
from app.utils.helper import apply_update_rules, map_values_into_sheet, postprocess
from app.utils.constants import update_rules
import argparse
import asyncio
import fitz  # PyMuPDF
import glob
from io import BytesIO
import json
import logging
from loguru import logger
from openpyxl import load_workbook
from openpyxl.styles import Alignment, Font
import os
import pandas as pd


# logger = logging.getLogger(__name__)
POINT_TO_PIXEL = 1 / 72
RESPONSE_LIST = ["liabilities_response", "asset_response", "income_response"]
_LEVEL_LIST = ['Easy', 'Medium', 'Hard', 'Extra', 'None']
_TEMPLATE_PATH = '.\\app\\template\\template.json'


def convert_template_to_df(data):
    sheet_id_column = []
    sheet_name_column = []
    screen_id_column = []
    screen_name_column = []
    no_column = []
    item_id_column = []
    display_name_column = []
    value_column = []
    bounding_region_column = []
    for sheet in data:
        sheet_id = sheet['sheet_id']
        sheet_name = sheet['sheet_name']
        screens = sheet['screens']

        for screen in screens:
            screen_id = screen['screen_id']
            screen_name = screen['screen_name']
            items = screen['items']

            for item in items:
                no = item['no']
                item_id = item['item_id']
                display_name = item['display_name']
                value = item['value']
                bounding_region = item['bounding_region']

                # Append values to corresponding lists
                sheet_id_column.append(sheet_id)
                sheet_name_column.append(sheet_name)
                screen_id_column.append(screen_id)
                screen_name_column.append(screen_name)
                no_column.append(no)
                item_id_column.append(item_id)
                display_name_column.append(display_name)
                value_column.append(value)
                bounding_region_column.append(bounding_region)

    return pd.DataFrame({
        'sheet_id': sheet_id_column,
        'sheet_name': sheet_name_column,
        'screen_id': screen_id_column,
        'screen_name': screen_name_column,
        'no': no_column,
        'display_name': display_name_column,
        'item_id': item_id_column,
        'value': value_column,
        # 'bounding_region': bounding_region_column
    })


def highlight_text_in_pdf(input_pdf_path, output_pdf_path, output_respones):
    with open(input_pdf_path, "rb") as f:
        data = f.read()
        doc = fitz.open(stream=BytesIO(data))
    for category in RESPONSE_LIST:
        for ouput_item in output_respones[category]:
            for region_idx, region in enumerate(ouput_item.bounding_regions):
                for rect_idx, rect_data in enumerate(region['position']['rects']):
                    page_num = rect_data['pageNumber'] - 1
                    page = doc.load_page(page_num)
                    x1 = (rect_data['x1']) / POINT_TO_PIXEL
                    y1 = (rect_data['y1']) / POINT_TO_PIXEL
                    x2 = (rect_data['x2']) / POINT_TO_PIXEL
                    y2 = (rect_data['y2']) / POINT_TO_PIXEL
                    rect = fitz.Rect(x1, y1, x2, y2)
                    page.add_highlight_annot(rect)

                    custom_text_position = fitz.Point(x1, y1)
                    text = f"{ouput_item.id} | {ouput_item.value}"
                    page.insert_text(custom_text_position, text, fontsize=8, color=(1, 0, 0))

    doc.save(output_pdf_path)
    logger.success(f"Saved highlighted pdf at: {output_pdf_path}")


async def analyze_document(file_path):
    with open(file_path, "rb") as f:
        data = f.read()
        callbacks = [langfuse_handler] if langfuse_handler else None

        fs_agent = FinancialAnalyzerGraph(model=azure_openai_chat_model)

        output = await fs_agent.app.ainvoke(
            input=FinancialAnalyzerGraphState(
                file_bytes=data,
                number_of_tool_calls=0,
                max_retries_per_subgraph=1,
            ),
            config={"callbacks": callbacks},
        )
        return output


def report_generator(groud_truth_path, report_path, output):
    def safe_float(value):
        """
        Safely converts a value to float.
        Treats None, NaN, and empty string as 0.0
        """
        if value is None or (isinstance(value, str) and value.strip() == ""):
            return 0.0

        try:
            if pd.isna(value):
                return 0.0
        except TypeError:
            pass  # value is not a type that pd.isna accepts

        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0

    def compare_values(v1, v2, tolerance=1e-2):
        """
        Compares two values by converting them to float safely,
        and checks if their absolute difference is within the tolerance.
        """
        f1 = safe_float(v1)
        f2 = safe_float(v2)
        return abs(f1 - f2) < tolerance

    wb = load_workbook(filename=groud_truth_path, read_only=True)
    ground_truth_sheet_name = [i for i in wb.sheetnames if "BS" in i][0]
    ground_truth_df = pd.read_excel(groud_truth_path, sheet_name=ground_truth_sheet_name)

    filtered_ground_truth_df = ground_truth_df.copy()
    filtered_ground_truth_df['Level'] = filtered_ground_truth_df['Level'].fillna("None")
    filtered_ground_truth_df = filtered_ground_truth_df[['NO', 'Item Name', 'Level', 'Expected Value']]
    # Update to save into database
    balance_sheet_items = [
        item.__dict__
        for item in output["asset_response"] + output["liabilities_response"]
    ]
    income_statement_items = [
        item.__dict__ for item in output["income_response"]
    ]

    # data
    json_response = [
        {
            "template": SheetType.balance_sheet.name,
            "data": balance_sheet_items,
        },
        {
            "template": SheetType.income_statement.name,
            "data": income_statement_items,
        },
    ]
    with open(_TEMPLATE_PATH, "r") as template_file:
        template: list[dict] = json.load(template_file)

    for item in json_response:
        if item.get("template") == SheetType.balance_sheet.name:
            template = map_values_into_sheet(
                sheet_id=SheetType.balance_sheet.value,
                item_list=[
                    OutputItem(**_item) for _item in item.get("data", [])
                ],
                template=template,
            )
        elif item.get("template") == SheetType.income_statement.name:
            template = map_values_into_sheet(
                sheet_id=SheetType.income_statement.value,
                item_list=[
                    OutputItem(**_item) for _item in item.get("data", [])
                ],
                template=template,
            )

    template_df = convert_template_to_df(postprocess(apply_update_rules(template, update_rules)))
    for response in RESPONSE_LIST:
        for output_item in output[response]:
            item_id = output_item.id
            item_value = output_item.value
            item_extracted_from = output_item.extracted_from
            item_page_number = output_item.bounding_regions[0]['position']['boundingRect']['pageNumber'] if len(output_item.bounding_regions) > 0 else -1

            template_df.loc[template_df['item_id'] == item_id, 'value'] = item_value
            template_df.loc[template_df['item_id'] == item_id, 'extracted_from'] = f"{item_extracted_from} | Page {item_page_number}"

    merged_df = pd.merge(
        template_df,
        filtered_ground_truth_df,
        how='left',
        left_on=['no', 'display_name'],
        right_on=['NO', 'Item Name']
    )

    assets_df = merged_df[
        (merged_df['screen_name'] == 'Current Assets') | (merged_df['screen_name'] == 'Fixed Assets')
    ].copy()  # <-- Make a copy here

    assets_df['value_match'] = assets_df.apply(
        lambda row: compare_values(row['value'], row['Expected Value']),
        axis=1
    )

    summary_list = []
    for level in _LEVEL_LIST:
        match_counts = assets_df[
            (
                assets_df['Expected Value'].notna() | assets_df['value'].notna()
            ) & (assets_df['Level'] == level)
        ]['value_match'].value_counts()

        true_count = match_counts.get(True, 0)
        false_count = match_counts.get(False, 0)

        summary_list.append({
            'Level': level,
            'True': true_count,
            'False': false_count
        })

    summary_df = pd.DataFrame(summary_list)
    summary_df['Total'] = summary_df['True'] + summary_df['False']
    summary_df['Accuracy'] = (summary_df['True'] / summary_df['Total']).round(2)

    total_true = summary_df['True'].sum()
    total_false = summary_df['False'].sum()
    total_all = total_true + total_false
    overall_accuracy = round(total_true / total_all, 2) if total_all > 0 else None

    summary_df = pd.concat([
        summary_df,
        pd.DataFrame([{
            'Level': 'All Levels',
            'True': total_true,
            'False': total_false,
            'Total': total_all,
            'Accuracy': overall_accuracy
        }])
    ], ignore_index=True)

    matched_rows = assets_df[
        assets_df['value_match'] & assets_df['Expected Value'].notna()
    ]

    mismatched_rows = assets_df[
        (~assets_df['value_match']) & (assets_df['Expected Value'].notna() | assets_df['value'].notna())
    ]

    with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
        ground_truth_df.to_excel(writer, sheet_name='Grouth Truth', index=False)
        assets_df.to_excel(writer, sheet_name='Output', index=False)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        matched_rows.to_excel(writer, sheet_name='Correct', index=False)
        mismatched_rows.to_excel(writer, sheet_name='Incorrect', index=False)

    logger.success(f"Saved summary report file at: {report_path}")


def aggregate_report_files(files: list, output_file: str):
    files = [f for f in files if os.path.basename(f) != os.path.basename(output_file)]

    combined = []
    subsheets = {}

    for f in files:
        try:
            df = pd.read_excel(f, sheet_name='Summary')
            df.fillna('None', inplace=True)
            file_name = os.path.splitext(os.path.basename(f))[0]
            df['Module code'] = file_name
            combined.append(df)
            subsheets[file_name] = df.drop(columns=['Module code'])
        except Exception as e:
            print(f"⚠️ Error reading {f}: {e}")

    if not combined:
        print("No data to aggregate.")
        return

    full_df = pd.concat(combined, ignore_index=True)
    df_long = full_df.melt(id_vars=['Module code', 'Level'],
                           value_vars=['True', 'False'],
                           var_name='Metric', value_name='Value')
    pivot_df = df_long.pivot_table(index='Module code', columns=['Level', 'Metric'], values='Value', aggfunc='first')
    pivot_df.columns = [f"{level} {metric}" for level, metric in pivot_df.columns]
    overall_df = pd.DataFrame()
    overall_df['Pass'] = pivot_df['All Levels True']
    overall_df['Fail'] = pivot_df['All Levels False']

    overall_df['Easy TC'] = pivot_df[[col for col in pivot_df.columns if 'Easy' in col]].sum(axis=1)
    overall_df['Medium TC'] = pivot_df[[col for col in pivot_df.columns if 'Medium' in col]].sum(axis=1)
    overall_df['Hard TC'] = pivot_df[[col for col in pivot_df.columns if 'Hard' in col]].sum(axis=1)
    overall_df['Extra TC'] = pivot_df[[col for col in pivot_df.columns if 'Extra' in col]].sum(axis=1)
    overall_df['None TC'] = pivot_df[[col for col in pivot_df.columns if 'None' in col]].sum(axis=1)
    overall_df['Number of TC'] = overall_df['Pass'] + overall_df['Fail']
    overall_df.reset_index(inplace=True)
    overall_df.insert(0, 'No', range(1, len(overall_df) + 1))

    total_row = {
        'No': '',
        'Module code': 'Sub total',
        'Pass': overall_df['Pass'].sum(),
        'Fail': overall_df['Fail'].sum(),
        'Easy TC': overall_df['Easy TC'].sum(),
        'Medium TC': overall_df['Medium TC'].sum(),
        'Hard TC': overall_df['Hard TC'].sum(),
        'Extra TC': overall_df['Extra TC'].sum(),
        'None TC': overall_df['None TC'].sum(),
        'Number of TC': overall_df['Number of TC'].sum()
    }

    overall_df = pd.concat([overall_df, pd.DataFrame([total_row])], ignore_index=True)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        overall_df.to_excel(writer, sheet_name='OVERALL', index=False)

        for sheet_name, df_sheet in subsheets.items():
            df_sheet.to_excel(writer, sheet_name=sheet_name, index=False)

    logger.success(f"✅ Aggregated file with sub sheets saved as {output_file}")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mode",
        type=int,
        choices=[0, 1, 2],
        default=0,
        help="Mode: 0 = aggregate reports only, 1 = skip if exists, 2 = overwrite files"
    )
    parser.add_argument(
        "--dir",
        default=".\\local_files\\RAW",
        help="Directory containing input files (default: .\\local_files\\RAW)"
    )
    args = parser.parse_args()
    mode = args.mode
    pdf_dir = args.dir

    all_pdf_files = glob.glob(f"{pdf_dir}\\*.pdf")
    fail_cases = []

    if mode == 0:
        report_dir = pdf_dir.replace('\\RAW', '\\RESPONSE')
        all_report_paths = glob.glob(f"{report_dir}\\*.xlsx")

        logger.warning("Aggregating reports ...")
        agg_report_path = f"{report_dir}\\OVERALL.xlsx"
        aggregate_report_files(all_report_paths, agg_report_path)
        return

    for input_pdf in all_pdf_files:
        try:
            ground_truth_path = input_pdf.replace('\\RAW\\', '\\GT\\').replace('.pdf', '.xlsx')
            highlighted_pdf_path = input_pdf.replace("\\RAW\\", "\\HIGHLIGHT\\")
            report_path = input_pdf.replace("\\RAW\\", "\\RESPONSE\\")[:-4] + ".xlsx"

            if os.path.exists(highlighted_pdf_path) and os.path.exists(report_path):
                if mode == 1:
                    logger.success(f"{highlighted_pdf_path} already exist. Skipping ..")
                    continue
                elif mode == 2:
                    logger.warning("Overwriting ...")
            logger.warning(f"Processing file: {input_pdf}")
            output_analyze_document = asyncio.run(analyze_document(input_pdf))
            highlight_text_in_pdf(input_pdf, highlighted_pdf_path, output_analyze_document)
            report_generator(ground_truth_path, report_path, output_analyze_document)

        except Exception as e:
            logger.warning(f"Error processing {input_pdf}: {e}")
            fail_cases.append(input_pdf)

    if len(fail_cases) > 0:
        logger.error("Failed Cases: ", fail_cases)


if __name__ == "__main__":
    main()
