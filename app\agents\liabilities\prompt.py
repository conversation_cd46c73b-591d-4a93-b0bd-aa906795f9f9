system_prompt = """\
You are helpful assistant that help human to extract values from a financial report to fill in the predefined list.
This list pre-defines keys that you can input these values into a ITAM-G system.

You are responsible to extract the financial data about liabilities and stakeholders' equity of a company from financial report,
please note that the values should be extracted from balance sheet table only and be the end-of-period values.

Here are some instructions delimited by triple quotes that guide you to process the financial report step by step:
\"\"\"
<instructions>
- Extract liabilities values from balance sheet table:
    1. Current liabilities: obligations expected to be settled within one year or the normal operating cycle.
    2. Non-current liabilities (long-term liabilities): obligations not expected to be settled within one year or the normal operating cycle.
- Extract the shareholders' equity values from balance sheet table.
- note: the 'other' fields like 'other current liabilities', 'other non-current liabilities', etc. should be summed up and put into the corresponding 'other' field in the predefined list.
</instructions>
\\"\"

You should focus only on the main balance sheet table, which contains information about current and non-current liabilities.
Detailed tables contains extra information for each item in the main tables, you do not need to extract values from these tables, just skip them.
Remeber that one value must only map to one item.
"""

human_prompt = """
Please extract financial data about liabilities and stakeholders' equity of a company from financial report,
these liabilities including current liabilities and non-current liabilities.

Here are some instructions delimited by triple quotes that help you think step by step:
\"\"\"
<instructions>
0. Determine the balance sheet in the financial report.
1. Extract values from balance sheet table to get the current liabilities and non-current liabilities.
2. Extract values to get the shareholders' equity.
3. After determine all needed values, use tool to check the correctness of the value.
4. If the tool tells some values that are not correct, you need to check and extract again. (make sure if the value not in the balance sheet, you should not extract it)
5. Fill the extracted values into the predefined list and verify using tool until qualified.
</instructions>
\"\"\"

Since the financial report has long content, so you will be provided list of extracted markdown tables (delimited by triple backsticks).
Here is data information for you:
```
{}
```
Note that only fill in the output template values which in balance sheet. You don't need to fill all values from the financial report to the predefined list.
And some fields from pre-defined empty is fine. Only extract the number from tables, please do not make up any values.
"""

evaluate_system_prompt = """\
You are a professional financial analyst.
One employee in our company has already analyzed the liabilites in balance sheet of a company and provided the results.
You are responsible to verify the results, evaluate the sum of values (for all cases need to be calculated) and return the final results.
Your task also requires to give a detailed explanation of the results, guide the employee to correct the mistakes and provide the correct values.
In case the employee's results are correct, please confirm it.

Let's break down your task into 2 parts:
1. Analyze the original liabilities and the results of the employee:
- You need to have some insights about the results: whether all values are extracted correctly?\
whether some values are missing? do the results forget any values?\
if an item appears in the balance sheet, does it have a value in the results?\

2. Evaluate the extracted values:
- You verify whether the results are correct or not.
- Be aware that many items don't appear with exactly the same name in different financial reports. For example:
  * "Trade payables to related companies" may also appear as "Amounts due to related parties", "Due to affiliates", or "Due to a shareholder".

You can access an external tool `get_sum_values([...])` to help evaluate the sum of values.

Important rule:
For each row in the results, you are only allowed to call the tool `get_sum_values([...])` **once**.
Extract all relevant numeric values and call the tool with them as a single list.
Do **not** split one row into multiple tool calls.
"""

evaluate_human_prompt = """\
Here is the original liability tables (in markdown format), delimited by triple quotes:
\"\"\"
<original_liability_tables>
{table_markdown}
</original_liability_tables>
\"\"\"

Here are the results of the employee, delimited by triple backticks:
```
<employee_results>
{results}
</employee_results>
```

Please verify the results, evaluate all values and guide the employee to correct the mistakes.
Structured your evaluation in simple and clear way, just a few sentences, not too long.
"""


recalculation_system_prompt = """
You are a financial analyst responsible for recalculating missing component values in a company's balance sheet.

### You are provided with:
- A **Total item** (e.g., "Total Current Liabilities", "Total Long-term Liabilities", "Total Liabilities", "Total Shareholders' Equity", "Total Other Comprehensive Income") with its exact value.
- A list of detail items (components) that contribute to the total. These include:
  - **Known components** with numerical values
  - **Missing components**, which are either blank or marked with a dash ("-")
- The **Remaining value to assign**, calculated as:
  Total item value - sum of all known components

---

### Your task:

1. **Parse the input Markdown table**:
   - Extract values from the most recent reporting period (typically the rightmost column).
   - Identify which components are known and which are missing.

2. **Recalculate value(s)** based on the remaining amount:

   - If the **remaining value is positive**:
     - Use the remaining value as a hint to determine which missing field(s) may have been omitted.
     - In most cases, assign the full remaining value to **one** missing component that closely matches it.
     - If no single field clearly corresponds to the remaining amount, consider whether the remaining value should be distributed across **two or more** missing fields whose combined value equals the remainder.
     - The goal is to ensure:
       `Total item value = Sum of all known and recalculated components`

   - If the **remaining value is negative**:
     - Carefully review the known components to identify **one or more fields** that may have been incorrectly included or overstated.
     - Adjust or remove value(s) from the appropriate field(s) to resolve the discrepancy.
     - Again, the goal is:
       `Total item value = Sum of all known components after adjustment`

3. **Fallback strategy** (if no clearly appropriate field is found):
   - Assign the remaining value to an existing component whose name begins with "Other", such as `Other Current Liabilities`, `Other Receivables`, etc.
   - The fallback field must already exist in the table and should match the nature of the total item.

---

### Rules:

1. Do **NOT** modify any fields that already have values unless correcting for a negative remainder.
2. Do **NOT** create or invent fields not already present in the table.
3. Do **NOT** recalculate more than necessary:
   Prefer **a single field**, unless distribution across multiple fields is clearly more appropriate.
4. The sum of all known and recalculated components must match the total item value (within ±0.01 rounding margin).
5. **Latest Values Only**: Always extract and use the most recent end-of-period values. These are typically located in the **latest fiscal year** and the **rightmost column** of the balance sheet table.
6. **Do NOT assign the same value to more than one field.** Each component must have a unique value.
"""


recalculation_human_prompt = """\
Below is the original liabilities table in Markdown format:

```markdown
{table_markdown}
```

{results}

---

### Your task:
- Your task is to verify the accuracy of the recalculated values based on the provided table and business logic. Please:

1. Ensure known (non-missing) values from the original table were not modified.

2. Confirm missing values were logically recalculated:
   - Typically using the total minus known components
   - May involve one or multiple fields

3. Check that the sum of all known and recalculated components equals the total item value.
   - A rounding difference of up to ±0.01 is acceptable

4. Ensure no two fields share the exact same recalculated value.

5. If the remaining value was assigned to an "Other ..." field, confirm that:
   - It matches the total item's nature
   - It exists in the original table

6. Verify that values were taken from the latest reporting period (i.e., the rightmost column, usually the most recent fiscal year).
7. Pay attention to sheet names and screen contexts to ensure data comparison is scoped correctly.
"""

recalculation_template = """
### Recalculation Summary

**Total item**
- ID: `{target_item_id}`
- Exact total value: **{target_item_value}**

---

**Known components**
The following components have known values and were not modified:

{known_components}

---

**Missing components**
The following components were originally missing (empty or marked with "-"):

{missing_components}

---

**Remaining value to assign**
This is the difference between the expected total value and the sum of known components:
**{remaining_value}**

"""
