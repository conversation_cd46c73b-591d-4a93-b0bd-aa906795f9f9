# Import necessary libraries and modules
import ssl

import socketio
import uvicorn
from dotenv import load_dotenv
from fastapi import APIRouter, FastAPI
from loguru import logger
from starlette.requests import Request
from starlette.responses import Response

from app.api import api_router
from app.config.logging_config import setup_logging
from app.database.db import SessionLocal

load_dotenv()
# Create a FastAPI instance


ssl._create_default_https_context = ssl._create_unverified_context

sio = socketio.AsyncServer(
    async_mode="asgi",
    cors_allowed_origins="*",  # Match your client URL
    logger=True,
    engineio_logger=True,
)

app = FastAPI(
    title="CISD BP Analysis Agent System V0.0.1",
    description="Welcome to the CISD BP Analysis Agent System's API documentation!",
)
setup_logging()

logger.info("Starting app")


# Dictionary to keep track of connected clients
clients = {}


router = APIRouter()
router.include_router(api_router)

app.include_router(router)


@app.middleware("http")
async def db_session_middleware(request: Request, call_next):
    response = Response("Internal server error", status_code=500)
    try:
        request.state.db = SessionLocal()
        response = await call_next(request)
    except Exception as e:
        logger.exception(e)
        raise e from None
    finally:
        request.state.db.close()
    return response


@app.get("/healthcheck")
async def healthcheck():
    await resource_healthcheck()
    return {"status": "OK", "object": "health"}


@app.get("/")
def root():
    return {"message": "Welcome to the CISD BP Analysis Agent System project"}


async def resource_healthcheck():
    pass
    # import traceback

    # from app.chains.search.base import AzureSearchService
    # from app.chains.search.model import IndexNames
    # from app.chains.search.service import (
    #     batch_insert_initialize_service,
    #     initialize_all_az_search_service,
    # )
    # from app.utils.azure_openai_client import (
    #     azure_openai_gpt4o,
    #     azure_openai_gpt4o_mini,
    # )

    # _create_preprocess_index = False
    # _create_ticket_index = False
    # try:
    #     await azure_openai_gpt4o.ainvoke("Hello")
    #     logger.info("Azure OpenAI GPT4o is available")
    # except Exception:
    #     logger.error(f"Azure OpenAI GPT4o Error: {traceback.format_exc()}")

    # try:
    #     await azure_openai_gpt4o_mini.ainvoke("Hello")
    #     logger.info("Azure OpenAI GPT4o Mini is available")
    # except Exception:
    #     logger.error(f"Azure OpenAI GPT4o Error: {traceback.format_exc()}")

    # try:
    #     await azure_openai_gpt4o_mini.ainvoke("Hello")
    #     logger.info("Azure OpenAI GPT4o Mini is available")
    # except Exception:
    #     logger.error(f"Azure OpenAI GPT4o Mini Error: {traceback.format_exc()}")
    # try:
    #     az_service = AzureSearchService(index_name=IndexNames.index_name)
    #     _ = az_service.search_client.get_document_count()
    #     logger.info("Ticket index is available")
    # except Exception:
    #     logger.error(
    #         f"Azure Search Service for serving/ inference is not available: {traceback.format_exc()}"
    #     )
    #     _create_ticket_index = True
    # try:
    #     az_preprocess_service = AzureSearchService(
    #         index_name=IndexNames.preprocess_index_name
    #     )
    #     _ = az_preprocess_service.search_client.get_document_count()
    #     logger.info("Preprocess index is available")
    # except Exception:
    #     logger.error(
    #         f"Azure Search Service for pre-processing is not available: {traceback.format_exc()}"
    #     )
    #     _create_preprocess_index = True

    # # Initialize search services
    # try:
    #     if _create_ticket_index:
    #         initialize_all_az_search_service(IndexNames.index_name)
    #         await batch_insert_initialize_service(IndexNames.index_name)
    #         logger.info("Created Ticket index ")
    # except Exception:
    #     logger.error(f"Error creating ticket index: {traceback.format_exc()}")

    # try:
    #     if _create_preprocess_index:
    #         initialize_all_az_search_service(IndexNames.preprocess_index_name)
    #         await batch_insert_initialize_service(IndexNames.preprocess_index_name)
    #         logger.info("Created Preprocess index")
    # except Exception:
    #     logger.error(f"Error creating preprocess index: {traceback.format_exc()}")


# healthcheck()
@app.on_event("startup")
async def startup_event():
    # await resource_healthcheck()
    # await update_sequences()
    pass


# Mount Socket.IO on the ASGI app
socket_app = socketio.ASGIApp(sio, other_asgi_app=app)


# defining event handlers with Socket.IO
@sio.event
async def connect(sid, environ, auth):
    logger.debug(f"Client connected: {sid}")
    await sio.emit("message", {"msg": "Welcome!"}, room=sid)


@sio.event
async def disconnect(sid):
    logger.debug(f"Client disconnected: {sid}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
