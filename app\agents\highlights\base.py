from typing import Any

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from loguru import logger

from app.data_processing.model import Table
from app.schemas.highlights import ExtractHighlightInput, HighlightItems, OutputItem
from app.utils.azure_openai_client import azure_openai_chat_model

from .prompt import system_prompt
from .utils import (
    generate_table_cells_repr,
    merge_bounding_regions,
    transform_to_output_item,
)


def create_highlight_extractor_agent(
    llm: AzureChatOpenAI = azure_openai_chat_model,
) -> AzureChatOpenAI:
    """
    Create a highlight extractor agent with the specified language model.

    Args:
        llm (AzureChatOpenAI): The language model to be used for the agent.

    Returns:
        AzureChatOpenAI: The configured highlight extractor agent.
    """

    llm_with_structured_output = llm.with_structured_output(HighlightItems)
    return llm_with_structured_output


async def extract_highlights(
    inputs: Any,
    table_list: list[Table],
    prompt: str,
    llm: AzureChatOpenAI = create_highlight_extractor_agent(),
    config: dict = {},
) -> list[OutputItem]:

    output_items: list[OutputItem] = transform_to_output_item(inputs)

    table_list_repr = "\n".join(
        [generate_table_cells_repr(table) for table in table_list]
    )
    # run the models
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(
            content=prompt.format(
                items="\n".join([item.get_str() for item in output_items]),
                table=table_list_repr,
            )
        ),
    ]
    highlight_items: HighlightItems = await llm.ainvoke(messages, config=config)
    logger.debug("Finish extracting highlights")
    updated_output_items: list[OutputItem] = merge_bounding_regions(
        output_items=output_items,
        highlight_items=highlight_items,
        table_list=table_list,
    )

    return updated_output_items


async def abatch_extract_highlights(
    extract_highlights_inputs: list[ExtractHighlightInput],
    llm: AzureChatOpenAI = create_highlight_extractor_agent(),
    config: dict = {},
) -> list[list[OutputItem]]:
    """
    Extract highlights from multiple inputs using the provided language model.
    Args:
        extract_highlights_inputs (list[ExtractHighlightInput]): List of inputs for highlight extraction.
        llm (AzureChatOpenAI): The language model to be used for the agent.
        config (dict): Configuration options for the language model.
    Returns:
        list[list[OutputItem]]: List of lists of OutputItem objects with extracted highlights.
    """

    tables_list: list[list[Table]] = []
    messages_list = []
    output_items_list: list[list[OutputItem]] = []

    for highlight_input in extract_highlights_inputs:
        output_items: list[OutputItem] = transform_to_output_item(
            highlight_input.input_instance
        )
        output_items_list.append(output_items)

        table_list_repr = "\n".join(
            [generate_table_cells_repr(table) for table in highlight_input.table_list]
        )
        tables_list.append(highlight_input.table_list)
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(
                content=highlight_input.prompt.format(
                    items="\n".join([item.get_str() for item in output_items]),
                    table=table_list_repr,
                )
            ),
        ]
        messages_list.append(messages)

    highlight_items_list: list[HighlightItems] = await llm.abatch(
        messages_list, config=config
    )
    logger.debug("Finish extracting highlights")
    updated_output_items: list[list[OutputItem]] = [
        merge_bounding_regions(
            output_items=output_items_list[i],
            highlight_items=highlight_item,
            table_list=tables_list[0],
        )
        for i, highlight_item in enumerate(highlight_items_list)
    ]
    return updated_output_items
