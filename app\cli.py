import json
import os
from datetime import datetime

import click
import uvicorn
from alembic import command as alembic_command
from alembic.config import Config as AlembicConfig
from loguru import logger
from sqlalchemy import MetaD<PERSON>, inspect, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import text

from app.database.db import Base, engine
from app.settings import Database

db = Database()


@click.group()
def chatfile_cli():
    """Command-line interface to Chatbot."""
    pass


@chatfile_cli.group("database")
def chatfile_database():
    """Command-line interface to Chatbot database commands."""
    click.secho(
        f"Connecting to database {Database.postgres_db_name} with host {Database.postgres_db_host} and port {Database.postgres_db_port}",
        fg="red",
    )


@chatfile_cli.group("server")
def chatfile_server():
    """Command-line interface to Chatbot server commands."""
    pass


@chatfile_server.command("start")
def server_start():
    uvicorn.run("app.main:socket_app", port=8000, host="0.0.0.0")


@chatfile_server.command("develop")
def server_develop():
    uvicorn.run("app.main:socket_app", reload=True, port=8000, host="0.0.0.0")


@chatfile_database.command("init")
def init_database():
    """Initializes a new database."""
    # Create all tables from the Base metadata
    # Base.metadata.create_all(engine)
    click.secho("Database schema created.", fg="green")

    # Path to the alembic.ini configuration file
    alembic_path = os.path.join(
        os.path.dirname(os.path.realpath(__file__)), "alembic.ini"
    )
    alembic_cfg = AlembicConfig(alembic_path)

    # Stamp the database with the initial migration version
    alembic_command.stamp(alembic_cfg, "c2e2f6def6f5")
    click.secho("Database stamped with initial version.", fg="green")

    # Upgrade to the initial version
    alembic_command.upgrade(alembic_cfg, "head")
    click.secho("Database upgraded to the head version.", fg="green")


@chatfile_database.command("revision")
@click.option("-m", "--message", default=None, help="Revision message")
@click.option(
    "--autogenerate",
    is_flag=True,
    help=(
        "Populate revision script with candidate migration "
        "operations, based on comparison of database to model"
    ),
)
@click.option(
    "--sql",
    is_flag=True,
    help=("Don't emit SQL to database - dump to standard output " "instead"),
)
def revision_database(message, autogenerate, sql):
    alembic_path = os.path.join(
        os.path.dirname(os.path.realpath(__file__)), "alembic.ini"
    )
    alembic_cfg = AlembicConfig(alembic_path)
    alembic_command.revision(
        config=alembic_cfg, message=message, autogenerate=autogenerate, sql=sql
    )
    click.secho("Success.", fg="green")


@chatfile_database.command("upgrade")
@click.option(
    "--tag",
    default=None,
    help="Arbitrary 'tag' name - can be used by custom env.py scripts.",
)
@click.option(
    "--sql",
    is_flag=True,
    default=False,
    help="Don't emit SQL to database - dump to standard output instead.",
)
@click.option("--revision", nargs=1, default="head", help="Revision identifier.")
def upgrade_database(tag, sql, revision):
    """Upgrades database schema to newest version."""
    from alembic import migration

    alembic_path = os.path.join(
        os.path.dirname(os.path.realpath(__file__)), "alembic.ini"
    )
    alembic_cfg = AlembicConfig(alembic_path)

    conn = engine.connect()
    context = migration.MigrationContext.configure(conn)
    current_rev = context.get_current_revision()
    if not current_rev:
        Base.metadata.create_all(engine)
        alembic_command.stamp(alembic_cfg, "head")
    else:
        alembic_command.upgrade(alembic_cfg, revision, sql=sql, tag=tag)
        click.secho(f"{alembic_path}", fg="red")

    click.secho("Success.", fg="green")


@chatfile_database.command("downgrade")
@click.option(
    "--tag",
    default=None,
    help="Arbitrary 'tag' name - can be used by custom env.py scripts.",
)
@click.option(
    "--sql",
    is_flag=True,
    default=False,
    help="Don't emit SQL to database - dump to standard output instead.",
)
@click.option("--revision", nargs=1, default="head", help="Revision identifier.")
def downgrade_database(tag, sql, revision):
    """Downgrades database schema to next newest version."""
    alembic_path = os.path.join(
        os.path.dirname(os.path.realpath(__file__)), "alembic.ini"
    )
    alembic_cfg = AlembicConfig(alembic_path)

    if sql and revision == "-1":
        revision = "head:-1"

    alembic_command.downgrade(alembic_cfg, revision, sql=sql, tag=tag)
    click.secho("Success.", fg="green")


def entrypoint():
    """The entry that the CLI is executed from"""
    try:
        chatfile_cli()
    except Exception as e:
        click.secho(f"ERROR: {e}", bold=True, fg="red")


# Add this new command after the other database commands
@chatfile_database.command("dump")
@click.option(
    "--output",
    "-o",
    default=None,
    help="Output file path. Defaults to dump_YYYY-MM-DD.json",
)
@click.option(
    "--include_tables",
    "-i",
    multiple=True,
    help="Include specific tables in the dump. Defaults to all tables.",
)
def dump_database(output: str = None, include_tables: list[str] = None):
    """Dumps all database data to a JSON file."""
    try:
        # Generate default filename if none provided
        if not output:
            timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%S")
            output = f"dump_{timestamp}.json"

        # Get all table names from SQLAlchemy metadata
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        logger.info(f"Found {len(tables)} tables in database, including: {tables}")

        data = {}
        with engine.connect() as conn:
            for table in include_tables:
                if table not in tables:
                    logger.info(f"Table {table} not found in database")
                    continue
                # Query all rows from each table
                logger.info(f"Dumping table {table}")
                result = conn.execute(text(f'SELECT * FROM "{table}"'))
                rows = [dict(row._mapping) for row in result]
                data[table] = rows
                logger.info(f"Dumped {len(rows)} rows from {table}")

        # Write to JSON file
        with open(output, "w") as f:
            json.dump(data, f, default=str, indent=2)

        click.secho(f"Database dumped to {output}", fg="green")

    except Exception as e:
        click.secho(f"Failed to dump database: {e}", fg="red")
        raise


@chatfile_database.command("dump_column")
@click.argument("table_name")
@click.argument("column_name")
@click.option(
    "--output",
    "-o",
    default=None,
    help="Output file path. Defaults to dump_column_YYYY-MM-DD.json",
)
def dump_column(table_name: str, column_name: str, output: str = None):
    """Dumps a specific column from a table to a JSON file."""
    try:
        # Generate default filename if none provided
        if not output:
            timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%S")
            output = f"dump_column_{timestamp}.json"

        # Get the specific column data
        data = {}
        with engine.connect() as conn:
            logger.info(f"Dumping column {column_name} from table {table_name}")
            result = conn.execute(
                text(f'SELECT "ticket_id", "{column_name}" FROM "{table_name}"')
            )
            rows = [dict(row._mapping) for row in result]
            data[table_name] = rows
            logger.info(f"Dumped {len(rows)} rows from {table_name}")

        # Write to JSON file
        with open(output, "w") as f:
            json.dump(data, f, default=str, indent=2)

        click.secho(
            f"Column {column_name} from table {table_name} dumped to {output}",
            fg="green",
        )

    except Exception as e:
        click.secho(f"Failed to dump column: {e}", fg="red")
        raise


@chatfile_database.command("load")
@click.argument("input_file", type=click.Path(exists=True))
@click.option("--force", is_flag=True, help="Override existing data")
def load_database(input_file: str, force: bool):
    """Loads database data from a JSON file."""
    try:
        # Read JSON file
        with open(input_file, "r") as f:
            data = json.load(f)

        with engine.connect() as conn:
            # Check for existing data
            if not force:
                inspector = inspect(engine)
                for table in data.keys():
                    result = conn.execute(text(f'SELECT COUNT(*) FROM "{table}"'))
                    count = result.scalar()
                    if count > 0:
                        raise Exception(
                            f"Table {table} already contains data. Use --force to override."
                        )

            # Insert data table by table
            for table_name, rows in data.items():
                if rows:  # Skip empty tables
                    # Clear existing data if force flag is set
                    if force:
                        conn.execute(text(f'TRUNCATE TABLE "{table_name}" CASCADE'))
                        conn.commit()

                    # Insert rows
                    if rows:
                        columns = rows[0].keys()
                        column_names = ", ".join([f'"{col}"' for col in columns])

                        # Insert rows in batches
                        batch_size = 1000
                        for i in range(0, len(rows), batch_size):
                            batch = rows[i : i + batch_size]

                            # Prepare parameterized query
                            placeholders = ",".join(
                                [
                                    f'({",".join([":"+str(i)+"_"+str(j) for j in range(len(columns))])})'
                                    for i in range(len(batch))
                                ]
                            )

                            query = text(
                                f'INSERT INTO "{table_name}" ({column_names}) VALUES {placeholders}'
                            )

                            # Create parameters dict with proper type handling
                            params = {}
                            for idx, row in enumerate(batch):
                                for j, col in enumerate(columns):
                                    value = row[col]
                                    # Handle null values
                                    if value == "null" or value is None:
                                        value = None
                                    params[f"{idx}_{j}"] = value

                            conn.execute(query, params)
                            conn.commit()

                            click.secho(
                                f"Inserted batch of {len(batch)} rows into {table_name}",
                                fg="green",
                            )

        click.secho(f"Successfully loaded data from {input_file}", fg="green")

    except Exception as e:
        click.secho(f"Failed to load database: {str(e)}", fg="red")
        raise


@chatfile_database.command("update_column")
@click.argument("input_file", type=click.Path(exists=True))
@click.argument("table_name")
@click.argument("column_name")
@click.option("--force", is_flag=True, help="Override existing data")
def update_column(input_file: str, table_name: str, column_name: str, force: bool):
    """Updates a specific column in a table from a JSON file."""
    try:
        # Read JSON file
        with open(input_file, "r") as f:
            data = json.load(f)

        with engine.connect() as conn:
            # Check for existing data
            if not force:
                inspector = inspect(engine)
                result = conn.execute(text(f'SELECT COUNT(*) FROM "{table_name}"'))
                count = result.scalar()
                if count > 0:
                    raise Exception(
                        f"Table {table_name} already contains data. Use --force to override."
                    )

            # Update column data
            rows = data.get(table_name, [])
            if rows:
                for row in rows:
                    value = row.get(column_name)
                    ticket_id = row.get("ticket_id")
                    if value is not None and ticket_id is not None:
                        conn.execute(
                            text(
                                f'UPDATE "{table_name}" SET "{column_name}" = :value WHERE ticket_id = :ticket_id'
                            ),
                            {"value": value, "ticket_id": ticket_id},
                        )
                        conn.commit()

                click.secho(
                    f"Updated column {column_name} in table {table_name}", fg="green"
                )

    except Exception as e:
        click.secho(f"Failed to update column: {str(e)}", fg="red")
        raise


@chatfile_database.command("remove_all_tables")
def remove_all_tables():
    """Removes all tables from the database."""
    try:
        metadata = MetaData()
        metadata.reflect(bind=engine)

        with engine.begin() as conn:
            inspector = inspect(engine)
            tables = inspector.get_table_names()

            # Drop tables in transaction
            for table_name in tables:
                # Drop with proper quoting and cascade
                conn.execute(text(f'DROP TABLE IF EXISTS "{table_name}" CASCADE'))
                logger.info(f"Dropped table: {table_name}")
            conn.commit()
        click.secho("Successfully removed all non-protected tables.", fg="green")

    except SQLAlchemyError as e:
        click.secho(f"Database error: {str(e)}", fg="red")
        logger.error(f"Failed to remove tables: {str(e)}")
    except Exception as e:
        click.secho(f"Unexpected error: {str(e)}", fg="red")
        logger.error(f"Unexpected error during table removal: {str(e)}")


if __name__ == "__main__":
    entrypoint()
