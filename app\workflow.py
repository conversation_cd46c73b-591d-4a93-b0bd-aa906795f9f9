import io
import asyncio
import argparse
from datetime import datetime
from app.agents.base import FinancialAnalyzerGraphState

from app.utils.azure_openai_client import azure_openai_chat_model
from dotenv import load_dotenv
from loguru import logger
import json
from pathlib import Path
from app.agents import FinancialAnalyzerGraph
import time
import os
import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter
import shutil
from app.schemas.assets import (
    Assets,
    CurrentAssets,
    FixedAssets,
    IntangibleAssets,
    TangibleFixedAssets,
)
from app.schemas.base import SheetType
from app.schemas.highlights import OutputItem

from app.utils.helper import map_values_into_sheet

load_dotenv()
analyzer = FinancialAnalyzerGraph(model=azure_openai_chat_model)

file_to_sheet_map = {
    "ANIMESH FS": "ANIMESH",
    "AQUAPLAST FS": "AQUAPLAST",
    "ARLANXEO_FS DEC 2023": "ARLANXEO",
    "ATLANTA FS 2023C": "ATLANTAA",
    "ESON VN FS": "ESON_VN",
    "FORMOSA FS": "FORMOSA FS",
    "HKJ_FS DEC'23": "HKJ",
    "HOANGYEN_FS DEC'23": "HOANGYEN",
    "MTC_FS MAR'24": "MTC",
    "TPP_FS DEC'23": "TPP",
    "TURBOND_FS DEC'24": "TURBOND",
    "WINSOME GREEN_D&B REPORT": "WINSOME_GREEN"
}


def display_output(liabilities_data):
    """
    Display the liabilities data in a formatted way
    
    Args:
        liabilities_data: The liabilities response data to display
    """
    if hasattr(liabilities_data, "pretty_print"):
        # This is a response object with pretty_print method
        print(liabilities_data.pretty_print())
        return
        
    # Handle list of OutputItems
    if isinstance(liabilities_data, list):
        # Group by categories using dictionary
        categories = {}
        json_categories = {}
        for item in liabilities_data:
            # Extract category from the ID
            id_parts = item.id.split('/')
            
            if len(id_parts) >= 2:
                category = id_parts[1]  # Get the main category
                if category not in categories:
                    categories[category] = []
                    json_categories[category] = []
                
                categories[category].append(item)
                json_categories[category].append(item.model_dump())
            else:
                categories[id_parts[0]] = [item]
                json_categories[id_parts[0]]= [item.model_dump()]

            #     categories
        # Print by category
        output_str = ""
        for category, items in categories.items():
            # output_str += f"\n{category.upper().replace('_', ' ')}\n"
            # output_str += "-" * len(category)
            output_str += "\n"
            for item in items:
                # Extract the last part of the ID for display
                id_parts = item.id.split('/')
                name = id_parts[-1].replace('_', ' ')
                # Format value with commas for thousands
                value = f"{item.value}" if item.value is not None else "N/A"
                
                output_str +=f"  {name},{value},{item.extracted_from}\n"
                # print(output_str)
        return json_categories, output_str
    else:
        # Fallback for other data types
        print(liabilities_data)


async def run_analyzer(input_files, branch_name):
    # Use provided input files or default ones
    if not input_files:
        # Default input files
        fs_sample_dir = "local_files/RAW"
        pdf_files = [os.path.join(fs_sample_dir, f) for f in os.listdir(fs_sample_dir) if f.lower().endswith('.pdf')]
        logger.info(f"Found {len(pdf_files)} PDF files in {fs_sample_dir}")
        # pdf_files = [
        #                 # "data/PTBUMI.pdf",
        #                 "data/ARLANXEO_FS DEC 2023.pdf"
        # #              "FS Sample\ATLANTA FS 2023C.pdf",
        # #              "FS Sample\ARLANXEO_FS DEC 2023.pdf",
        # #              "FS Sample\ESON VN FS.pdf",
        # #              "FS Sample\ANIMESH FS.pdf",
        # #              "FS Sample\AQUAPLAST FS.pdf"
        #              ]
    else:
        pdf_files = input_files
        logger.info(f"Using provided input files: {pdf_files}")
    for file_path in pdf_files:
        # file_path = "new_FS\EMT.pdf"
        base_filename = Path(file_path).stem
        # Get current date in YYYYMMDD format
        current_date = datetime.now().strftime("%Y%m%d")
        combined_filename = f"outputs/{current_date}_{branch_name}/{base_filename}.csv"
        # if os.path.exists(combined_filename):
        #     logger.info(f"File {combined_filename} already exists, skipping.")
        #     continue
        os.makedirs(os.path.dirname(combined_filename), exist_ok=True)
        logger.info(f"Analyzing file: {file_path}")
        with open(file_path, "rb") as f:
            file_bytes = io.BytesIO(f.read())

        initial_state =  FinancialAnalyzerGraphState(
            file_bytes=file_bytes,
            max_retries_per_subgraph=2
        )
        try:
            output = await analyzer.app.ainvoke(initial_state)
            logger.info("Analysis completed successfully")
            
            with open(combined_filename, "w", encoding="utf-8") as f:
                # f.write("=== LIABILITIES ANALYSIS ===\n\n")
                liabilities_data, liabilities_str = display_output(output['liabilities_response'])
                f.write(liabilities_str)
            
            # with open(combined_filename, "a", encoding="utf-8") as f:
            #     f.write("\n\n=== ASSETS ANALYSIS ===\n\n")
            #     assets_data, assets_str = display_output(output['asset_response'])
            #     f.write(assets_str)
                
            # with open(combined_filename, "a", encoding="utf-8") as f:
            #     f.write("\n\n=== INCOME ANALYSIS ===\n\n")
            #     income_data, income_str = display_output(output['income_response'])
            #     f.write(income_str)
            logger.info(f"Output saved to {combined_filename}")        
                    
        except Exception as e:
            logger.error(f"Error running analyzer: {e}")
            import traceback
            logger.error(traceback.format_exc())
            logger.info(f"Failed to analyze file: {file_path}")
        


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Financial Analyzer Workflow")

    parser.add_argument(
        "--input-files",
        nargs="*",
        default=None,
        help="List of input PDF file paths to analyze. If not provided, uses default files in code."
    )

    parser.add_argument(
        "--branch-name",
        type=str,
        required=True,
        help="Branch name to use in output path (required)"
    )

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()
    json_response = asyncio.run(run_analyzer(args.input_files, args.branch_name))
    liabilities_response = json_response['liabilities_response']


    # for item in json_response:
    #     if item.get("template") == SheetType.balance_sheet.name:
    #         template = map_values_into_sheet(
    #             sheet_id=SheetType.balance_sheet.value,
    #             item_list=[
    #                 OutputItem(**_item) for _item in item.get("data", [])
    #             ],
    #             template=template,
    #         )
    #         # screen 3 current liabilities
    #         current_liabilities = template[0]['screens'][2]['items']
    #         for item in current_liabilities:
    #             item_display_name = item.get("display_name")
    #             if item['value'] is None:
    #                 continue
    #             # Find the row number for this item in the Excel worksheet
    #             row_found = None
    #             for idx, row in excel_wb.iterrows():
    #                 if row.get('Item Name') == item_display_name:
    #                     row_found = idx
    #                     logger.info(f"Found {item_display_name} at row {row_found}")
    #                     if row_found is not None:
    #                         logger.info(f"Setting value {item['value']} for {item_display_name}")
    #                         excel_wb.at[row_found, 'Value (03/05)'] = item["value"]
    #         # screen 4 non-current liabilities
    #         current_liabilities = template[0]['screens'][3]['items']
    #         for item in current_liabilities:
    #             item_display_name = item.get("display_name")
    #             if item['value'] is None:
    #                 continue
    #             # Find the row number for this item in the Excel worksheet
    #             row_found = None
    #             for idx, row in excel_wb.iterrows():
    #                 if row.get('Item Name') == item_display_name:
    #                     row_found = idx
    #                     logger.info(f"Found {item_display_name} at row {row_found}")
    #                     if row_found is not None:
    #                         logger.info(f"Setting value {item['value']} for {item_display_name}")
    #                         excel_wb.at[row_found, 'Value (03/05)'] = item["value"]
    #         logger.info(f"Mapping to excel")
    #     elif item.get("template") == SheetType.income_statement.name:
    #         template = map_values_into_sheet(
    #             sheet_id=SheetType.income_statement.value,
    #             item_list=[
    #                 OutputItem(**_item) for _item in item.get("data", [])
    #             ],
    #             template=template,
    #         )
    # # Save the workbook
    # excel_file_path = file_path.replace(".pdf", "_output.csv")
    # excel_wb.to_csv(excel_file_path, index=False)