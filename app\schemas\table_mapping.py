from typing import List

from pydantic import BaseModel, Field


class TableMapping(BaseModel):
    """ """

    balance_sheet: str = Field(
        description="A string containing multiple Markdown tables related to balance sheet, concatenated using double newlines."
    )
    income_statement: str = Field(
        description="A string containing multiple Markdown tables related to income statement, concatenated using double newlines."
    )


class BalanceSheetTable(BaseModel):
    """
    Output model that represents the IDs of tables determined to be balance sheet tables
    from a financial statement.
    """

    table_ids: List[str] = Field(
        description="List of table IDs that are identified as balance sheet tables. Each ID should match the format used in the source document, such as '/tables/3'."
    )


class IncomeStatementTable(BaseModel):
    table_ids: list[str] = Field(
        description="List of table ids that are income statement tables"
    )
