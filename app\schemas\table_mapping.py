from typing import List

from pydantic import BaseModel, Field


class TableMapping(BaseModel):
    """ """

    balance_sheet: str = Field(
        description="A string containing multiple Markdown tables related to balance sheet, concatenated using double newlines."
    )
    income_statement: str = Field(
        description="A string containing multiple Markdown tables related to income statement, concatenated using double newlines."
    )


class DetailTableIDs(BaseModel):
    """
    Predefined categories for detail tables that break down components of the balance sheet.
    Each field contains a list of table IDs relevant to that specific category.
    """

    # ASSETS
    inventories: List[str] = Field(
        default_factory=list,
        description="Tables detailing inventory breakdowns such as raw materials, work-in-progress (WIP), finished goods, and merchandise.",
    )
    current_assets: List[str] = Field(
        default_factory=list,
        description="Tables for current assets not classified elsewhere, such as prepaid expenses, short-term receivables, and other liquid current assets.",
    )
    tangible_fixed_assets: List[str] = Field(
        default_factory=list,
        description="Tables breaking down tangible fixed assets including land, buildings, machinery, equipment, and accumulated depreciation.",
    )
    intangible_assets: List[str] = Field(
        default_factory=list,
        description="Tables detailing intangible assets such as goodwill, patents, software, trademarks, and licenses.",
    )
    investment_assets: List[str] = Field(
        default_factory=list,
        description="Tables listing financial investments, subsidiaries, equity holdings, and joint ventures.",
    )
    fixed_assets: List[str] = Field(
        default_factory=list,
        description="Tables for non-current assets not classified elsewhere, such as deferred tax assets, long-term deposits, or other fixed holdings.",
    )

    # LIABILITIES
    current_liabilities: List[str] = Field(
        default_factory=list,
        description="Tables describing short-term obligations such as trade payables, accrued expenses, and short-term borrowings.",
    )
    long_term_liabilities: List[str] = Field(
        default_factory=list,
        description="Tables showing long-term debts and obligations including bonds payable, long-term loans, lease liabilities, and pensions.",
    )

    # EQUITY
    equity: List[str] = Field(
        default_factory=list,
        description="Tables detailing shareholders' equity including share capital, retained earnings, and reserves.",
    )
    other_comprehensive_income: List[str] = Field(
        default_factory=list,
        description="Tables covering components of other comprehensive income such as unrealized gains/losses, currency translation adjustments, and actuarial gains/losses.",
    )


class BalanceSheetTable(BaseModel):
    """
    Output model for classifying tables from a financial statement.

    It includes primary balance sheet tables and detail tables grouped by fixed categories.
    """

    table_ids: List[str] = Field(
        description="List of table IDs identified as balance sheet tables. Format: '/tables/3', etc."
    )
    detail_table_ids: DetailTableIDs = Field(
        description="Predefined categories mapping to table IDs that support balance sheet items."
    )


class IncomeStatementTable(BaseModel):
    table_ids: list[str] = Field(
        description="List of table ids that are income statement tables"
    )
