ANALYSIS-AZURE-OPENAI-API-KEY=
ANALYSIS-AZURE-OPENAI-ENDPOINT=
ANALYSIS-AZURE-OPENAI-API-VERSION=
ANALYSIS-AZURE-OPENAI-DEPLOYMENT=
ANALYSIS-AZURE-OPENAI-MODEL-NAME=
# gpt4-mini
ANALYSIS-FAST-AZURE-OPENAI-API-KEY=
ANALYSIS-FAST-AZURE-OPENAI-ENDPOINT=
ANALYSIS-FAST-AZURE-OPENAI-API-VERSION=
ANALYSIS-FAST-AZURE-OPENAI-DEPLOYMENT=
ANALYSIS-FAST-AZURE-OPENAI-MODEL-NAME=
# embedding model
ANALYSIS-AZURE-OPENAI-EMBEDDING-API-KEY=
ANALYSIS-AZURE-OPENAI-EMBEDDING-ENDPOINT=
ANALYSIS-AZURE-OPENAI-EMBEDDING-API-VERSION=
ANALYSIS-AZURE-OPENAI-EMBEDDING-DEPLOYMENT-NAME=

# azure document intelligence
ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-ENDPOINT=
ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-KEY=
ANALYSIS-AZURE-DOCUMENT-INTELLIGENCE-MODEL=

# database
ANALYSIS-DB-HOST=
ANALYSIS-DB-PORT=
ANALYSIS-DB-USER=
ANALYSIS-DB-PASSWORD=
ANALYSIS-DB-NAME=
ANALYSIS-CHECKPOINT-DB-NAME=

# Blob stroage
ANALYSIS-BLOB-CONNECTION-STRING=
ANALYSIS-BLOB-CONTAINER=

# jwt
ANALYSIS-JWT-SECRET-KEY=
ANALYSIS-JWT-ALG=

ANALYSIS-LANGFUSE-PUBLIC-KEY=
ANALYSIS-LANGFUSE-SECRET-KEY=
ANALYSIS-LANGFUSE-HOST=
