from enum import Enum, StrEnum, unique


class Message:
    # Internal server error
    INTERNAL_SERVER_ERROR = "Internal server error"
    MSG_PERMISSION_DENIED = "You don't have permission for this"
    MSG_GROUP_PERMISSION_DENIED = "User does not have access to the {group_name}"

    # Login
    MSG_LOGIN_INVALID_TOKEN_UNAUTHORIZED = "Invalid token, user unauthorized"
    MSG_LOGIN_NO_EMAIL = "Please enter your email"
    MSG_LOGIN_WRONG_EMAIL = "Incorrect email. Please try again"
    MSG_LOGIN_WRONG_CREDENTIAL = "You have entered wrong login information"
    MSG_LOGIN_WRONG_PWD = "Incorrect password. Please try again."
    MSG_LOGIN_INVALID_TOKEN = "Invalid token. Please check your token and try again."
    MSG_LOGIN_NOT_ALLOW_EMAIL = "The email address provided is not permitted in the system. Please verify your email address."

    # Logout
    MSG_LOGOUT_SUCCESS = "Logout successfully"

    # Register
    MSG_REGISTER_EMAIL_TAKEN = (
        "An account with the same email already exists. Please choose a different email"
    )

    # DELETED SUCCESSFULLY
    DELETED_SUCCESSFULLY = "Deleted successfully"

    # NOT FOUND
    MSG_ITEM_NOT_FOUND = "Item not found"
    MSG_LOGIN_ACCOUNT_NOT_FOUND = "Account not found"
    MSG_CONVERSATION_NOT_FOUND = "Conversation not found"
    MSG_DOCUMENT_NOT_FOUND = "Document not found"

    MSG_INVALID_REQUEST = "Invalid request"

    # Conversation chat
    MSG_CONVERSATION_FAIL_TO_CREATE = (
        "Oops! Failed to create new conversation. Please try again"
    )
    MSG_CONVERSATION_FAIL_TO_DELETE = (
        "Oops! Failed to delete conversation. Please try again"
    )
    MSG_CONVERSATION_FAIL_TO_GET_HISTORY = (
        "Oops! Failed to retrieve conversation history. Please try again"
    )
    MSG_CONVERSATION_FAIL_WHILE_STREAMING_FE = (
        "Oops! We couldn't process your request. Please try again"
    )

    MSG_CONVERSATION_FAIL_WHILE_STREAMING_BE = (
        "⚠️ Oops, something went wrong while processing your request. </br>"
        "Please try again in a moment or feel free to rephrase your question for better assistance 😊."
    )

    # Document
    MSG_UPLOAD_FILE_FAIL = "Unable to upload file. Please try again"

    # Document constructor
    MSG_DOCUMENT_CONSTRUCTION_FAILED = "Unable to construct document. Please try again"

    # Openai error messages
    MSG_OPENAI_API_RATE_LIMIT_ERROR = "OpenAI API rate limit exceeded, we cannot process your request at the moment. We will try again in 5 seconds..."

    MSG_OPENAI_API_BAD_REQUEST = "Bad request to OpenAI API, we cannot process your request at the moment. Please try again later with new conversation."
    MSG_OPENAI_MAX_RETRIES = "Sorry, we have tried calling OpenAI API multiple times but still cannot process your request. Please try again later with new conversation."

    MSG_INVALID_UPLOAD_FILE = "Invalid upload file. Please try again."


class FSConstants(StrEnum):
    TABLE_SEPARATOR = "\n\n"


@unique
class Constants(StrEnum):
    QA = "qa"
    CONVERSATION_SQL_TYPE = "SQL"
    COMMENT = "comment"
    RATING = "rating"
    FEEDBACK = "Feedback"
    LIKE = "Like"
    DISLIKE = "Dislike"
    BLOB_PDF_PREFIX = "PDF"
    BLOB_EXCEL_PREFIX = "EXCEL"
    PDF_TABLE_MARKDOWN = "table_markdown"
    PDF_TABLE_BOUNDING_BOX = "table_bb_list"


@unique
class GroupPermission(Enum):
    GENERAL_AGENT = "General Agent"
    DOCEDIT_AGENT = "DocEdit Agent"
    OUTPUT_FORM_AGENT = "Output Form Agent"
    TICKET_AGENT = "Ticket Agent"
    DATA_MANAGEMENT = "Data Management"
    USER_MANAGEMENT = "User Management"
    FEEDBACK_MANAGEMENT = "Feedback Management"
    DASHBOARD = "Dashboard"
    CISD_INTRANET_AGENT = "CISD Intranet Agent"
    BP_ANALYSIS_AGENT = "BP Analysis Agent"


update_rules = {
    "1": {  # sheet_id "1" - BS
        "1": [  # screen_id "1" - Current Assets
            (
                "12",
                ["8", "+", "9", "+", "10", "+", "11"],
            ),
            (
                "26",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                    "+",
                    "11",
                    "+",
                    "13",
                    "+",
                    "14",
                    "+",
                    "15",
                    "+",
                    "16",
                    "+",
                    "17",
                    "+",
                    "18",
                    "+",
                    "19",
                    "+",
                    "20",
                    "+",
                    "21",
                    "+",
                    "22",
                    "+",
                    "23",
                    "+",
                    "24",
                    "+",
                    "25",
                ],
            ),
            ("28", ["12"]),
            ("29", ["26"]),
            ("12", ["12", "==", "31"]),
            ("26", ["26", "==", "32"]),
            ("28", ["28", "==", "31"]),
            ("29", ["29", "==", "32"]),
        ],
        "2": [  # screen_id "2" - Fixed Assets
            (
                "9",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                ],
            ),
            ("13", ["10", "+", "11", "+", "12"]),
            (
                "30",
                [
                    "14",
                    "+",
                    "15",
                    "+",
                    "16",
                    "+",
                    "17",
                    "+",
                    "18",
                    "+",
                    "19",
                    "+",
                    "20",
                    "+",
                    "21",
                    "+",
                    "22",
                    "+",
                    "23",
                    "+",
                    "24",
                    "+",
                    "25",
                    "+",
                    "26",
                    "+",
                    "27",
                    "+",
                    "28",
                    "+",
                    "29",
                ],
            ),
            (
                "31",
                ["9", "+", "13", "+", "30"],
            ),
            ("32", ["screen1.29", "+", "31"]),
            ("34", ["9"]),
            ("35", ["13"]),
            ("36", ["30"]),
            ("37", ["31"]),
            ("38", ["32"]),
            ("9", ["9", "==", "40"]),
            ("13", ["13", "==", "41"]),
            ("30", ["30", "==", "42"]),
            ("31", ["31", "==", "43"]),
            ("32", ["32", "==", "44"]),
            ("34", ["34", "==", "40"]),
            ("35", ["35", "==", "41"]),
            ("36", ["36", "==", "42"]),
            ("37", ["37", "==", "43"]),
            ("38", ["38", "==", "44"]),
        ],
        "3": [  # current liabilities
            (
                "23",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                    "+",
                    "11",
                    "+",
                    "12",
                    "+",
                    "13",
                    "+",
                    "14",
                    "+",
                    "15",
                    "+",
                    "16",
                    "+",
                    "17",
                    "+",
                    "18",
                    "+",
                    "19",
                    "+",
                    "20",
                    "+",
                    "21",
                    "+",
                    "22",
                ],
            ),
            ("25", ["23"]),
            ("23", ["23", "==", "26"]),
            ("25", ["25", "==", "26"]),
        ],  # screen_id "3" - Current Liabilities
        "4": [
            (
                "14",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                    "+",
                    "11",
                    "+",
                    "12",
                    "+",
                    "13",
                ],
            ),
            ("15", ["screen3.23", "+", "14"]),
            ("21", ["16", "+", "17", "+", "18", "+", "19", "+", "20"]),
            ("27", ["22", "+", "23", "+", "24", "+", "25", "+", "26"]),
            ("28", ["21", "+", "27"]),
            ("31", ["28", "+", "29", "+", "30"]),
            ("32", ["15", "+", "31"]),
            ("35", ["14"]),
            ("36", ["15"]),
            ("37", ["21"]),
            ("38", ["27"]),
            ("39", ["28"]),
            ("40", ["31"]),
            ("41", ["32"]),
            ("14", ["14", "==", "42"]),
            ("15", ["15", "==", "43"]),
            ("21", ["21", "==", "44"]),
            ("27", ["27", "==", "45"]),
            ("31", ["31", "==", "46"]),
            ("32", ["32", "==", "47"]),
            ("35", ["35", "==", "42"]),
            ("36", ["36", "==", "43"]),
            ("37", ["37", "==", "44"]),
            ("38", ["38", "==", "45"]),
            # ("39", ["39", "==", ""]),
            ("40", ["40", "==", "46"]),
            ("41", ["41", "==", "47"]),
        ],  # screen_id "4" - Long-term Liabilities and Net Assets
    },
    "2": {  # sheet_id "2" - PL
        "1": [  # screen_id "1"
            ("3", ["1", "-", "2"]),  # item no "3"
            ("5", ["3", "-", "4"]),
            ("7", ["3"]),
            ("8", ["5"]),
            ("3", ["3", "==", "9"]),
            ("5", ["5", "==", "10"]),
        ],
        "2": [
            (
                "11",
                [
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                ],
            ),
            (
                "21",
                [
                    "13",
                    "+",
                    "14",
                    "+",
                    "15",
                    "+",
                    "16",
                    "+",
                    "17",
                    "+",
                    "18",
                    "+",
                    "19",
                    "+",
                    "20",
                ],
            ),
            ("23", ["screen1.5", "+", "11", "-", "21", "+", "22"]),
            ("26", ["23", "-", "24", "-", "25"]),
            ("30", ["26", "-", "27", "-", "28", "-", "29"]),
            ("11", ["11", "==", "31"]),
            ("12", ["12", "==", "32"]),
            ("21", ["21", "==", "32"]),
            ("23", ["23", "==", "33"]),
            ("26", ["26", "==", "34"]),
            ("30", ["30", "==", "35"]),
        ],
    },
}

recalculation_rules = {
    "1": {  # sheet_id "1" - BS
        "1": [  # screen_id "1" - Current Assets
            (
                "31",
                ["8", "+", "9", "+", "10", "+", "11"],
            ),
            # (
            #     "32",
            #     [
            #         "1",
            #         "+",
            #         "2",
            #         "+",
            #         "3",
            #         "+",
            #         "4",
            #         "+",
            #         "5",
            #         "+",
            #         "6",
            #         "+",
            #         "7",
            #         "+",
            #         "8",
            #         "+",
            #         "9",
            #         "+",
            #         "10",
            #         "+",
            #         "11",
            #         "+",
            #         "13",
            #         "+",
            #         "14",
            #         "+",
            #         "15",
            #         "+",
            #         "16",
            #         "+",
            #         "17",
            #         "+",
            #         "18",
            #         "+",
            #         "19",
            #         "+",
            #         "20",
            #         "+",
            #         "21",
            #         "+",
            #         "22",
            #         "+",
            #         "23",
            #         "+",
            #         "24",
            #         "+",
            #         "25",
            #     ],
            # ),
        ],
        "2": [  # screen_id "2" - Fixed Assets
            (
                "40",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                ],
            ),
            ("41", ["10", "+", "11", "+", "12"]),
            # (
            #     "42",
            #     [
            #         "14",
            #         "+",
            #         "15",
            #         "+",
            #         "16",
            #         "+",
            #         "17",
            #         "+",
            #         "18",
            #         "+",
            #         "19",
            #         "+",
            #         "20",
            #         "+",
            #         "21",
            #         "+",
            #         "22",
            #         "+",
            #         "23",
            #         "+",
            #         "24",
            #         "+",
            #         "25",
            #         "+",
            #         "26",
            #         "+",
            #         "27",
            #         "+",
            #         "28",
            #         "+",
            #         "29",
            #     ],
            # ),
        ],
        "3": [  # Screen_id "3" - Curent Liabilities
            (
                "27",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                    "+",
                    "11",
                    "+",
                    "12",
                    "+",
                    "13",
                    "+",
                    "14",
                    "+",
                    "15",
                    "+",
                    "16",
                    "+",
                    "17",
                    "+",
                    "18",
                    "+",
                    "19",
                    "+",
                    "20",
                    "+",
                    "21",
                    "+",
                    "22",
                ],
            ),
        ],
        "4": [  # screen_id "4" - longterm liabilities
            (
                "43",
                [
                    "1",
                    "+",
                    "2",
                    "+",
                    "3",
                    "+",
                    "4",
                    "+",
                    "5",
                    "+",
                    "6",
                    "+",
                    "7",
                    "+",
                    "8",
                    "+",
                    "9",
                    "+",
                    "10",
                    "+",
                    "11",
                    "+",
                    "12",
                    "+",
                    "13",
                ],
            ),
            ("44", ["screen3.23", "+", "14"]),
            ("45", ["16", "+", "17", "+", "18", "+", "19", "+", "20"]),
            ("46", ["22", "+", "23", "+", "24", "+", "25", "+", "26"]),
            ("47", ["21", "+", "27"]),
            ("48", ["28", "+", "29", "+", "30"]),
            ("49", ["15", "+", "31"]),
        ],
    },
}

# retry parameters for API callings
MAX_RETRIES = 3
RETRY_COUNT = 0
RETRY_DELAY = 5  # seconds
BATCH_SIZE_TOOL_CALLS = 16
