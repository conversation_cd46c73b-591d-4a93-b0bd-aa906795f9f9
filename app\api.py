from fastapi import APIRouter, Depends

from app.config.logging_config import log_request_info
from app.features.conversation.view import conversation_router
from app.features.document.view import router as document_router
from app.features.revision.view import database_router
from app.features.user.service import get_current_user

api_router = APIRouter(prefix="/api/v1")
authenticated_api_router = APIRouter()


authenticated_api_router.include_router(
    conversation_router,
    prefix="/conversations",
    tags=["conversation"],
)

authenticated_api_router.include_router(
    database_router,
    prefix="/database",
    tags=["database"],
)

authenticated_api_router.include_router(
    document_router,
    prefix="/documents",
    tags=["documents"],
)

# api_router.include_router(
#     indexing_router,
#     prefix="/index",
#     tags=["index"],
# )


api_router.include_router(
    authenticated_api_router,
    dependencies=[Depends(get_current_user), Depends(log_request_info)],
)
