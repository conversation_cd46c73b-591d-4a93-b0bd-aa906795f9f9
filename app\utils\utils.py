import asyncio
import re
import time
import traceback
from functools import wraps

import requests
from langchain_core.messages import AIMessage
from loguru import logger
from openai import (
    APIConnectionError,
    BadRequestError,
    LengthFinishReasonError,
    RateLimitError,
)
from openai.types.chat.chat_completion import ChatCompletion

from app.utils.constants import Message


def async_azure_openai_error_handling(func):
    async def wrapper(*args, **kwargs):
        for attempt in range(3):
            try:
                response = await func(*args, **kwargs)
                yield response
                return
            except RateLimitError as rate_limit_error:
                logger.error(
                    f"Attempt {attempt + 1}/3: Rate limit error: {rate_limit_error}"
                )
                # yield Message.MSG_OPENAI_API_RATE_LIMIT_ERROR
                await asyncio.sleep(5)
            except BadRequestError as bad_request_error:
                logger.error(
                    f"Attempt {attempt + 1}/3: Bad request error: {bad_request_error}"
                )
                yield Message.MSG_OPENAI_API_BAD_REQUEST
                break
            except LengthFinishReasonError as length_finish_reason_error:
                logger.error(
                    f"Attempt {attempt + 1}/3: Length finish reason error: {length_finish_reason_error}"
                )
                yield length_finish_reason_error.completion
                return
            except Exception as e:
                logger.error(f"Attempt {attempt + 1}/3: Error: {e}")
        else:
            yield Message.MSG_OPENAI_MAX_RETRIES
            raise Exception("Max retries reached")

    return wrapper


@async_azure_openai_error_handling
async def llm_ainvoke(llm, messages):
    return await llm.ainvoke(messages)


async def streaming_llm_ainvoke(llm, messages, runnable=None):
    try:
        async for message in llm_ainvoke(llm, messages):
            if isinstance(message, str):
                if runnable:
                    await runnable.ainvoke(message + "\n")
            elif isinstance(message, AIMessage) or isinstance(message, dict):
                return message
            elif isinstance(message, ChatCompletion):
                return message
    except Exception:
        logger.error("Error in streaming_llm_ainvoke")
        return


async def download_file(url, local_filename):
    logger.info(f"Downloading file from {url} to {local_filename}")
    # Send a GET request to the URL
    with requests.get(url, stream=True, timeout=600) as r:
        r.raise_for_status()
        # Open a local file with write-binary mode
        with open(local_filename, "wb") as f:
            # Write the content of the response to the local file
            for chunk in r.iter_content(chunk_size=8192):
                f.write(chunk)
    logger.info(f"Downloaded file {local_filename}")
    return local_filename


def call_openai(llm, prompt, retries=5, initial_delay=0.5):
    """
    Call the OpenAI API with exponential backoff retry logic.

    Args:
        llm: The OpenAI language model.
        prompt: The prompt to generate text from.
        retries: Maximum number of retries (default: 5)
        initial_delay: Initial delay between retries in seconds (default: 0.5)

    Returns:
        str: The generated text.

    Raises:
        Exception: If all retries fail or invalid response received.
    """
    for attempt in range(retries):
        try:
            response = llm.invoke(prompt)
            if not response:
                raise ValueError("Empty response received from API")
            return response

        except Exception as e:
            logger.warning(f"Attempt {attempt + 1}/{retries} failed: {str(e)}")
            if attempt == retries - 1:
                logger.error(f"Max retries reached. Final error: {str(e)}")
                raise f"Unexpected error: {traceback.format_exc()}"

            # Exponential backoff
            time.sleep(initial_delay)


def retry_on_rate_limit(retries=5, default_wait=15, max_wait=15):
    """
    Decorator to retry a function when encountering openai.RateLimitError (HTTP 429).
    It extracts the 'retry after' time from the error message and waits accordingly.

    :param retries: Maximum number of retries before failing
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args):
            last_exception = None
            for attempt in range(retries):
                try:
                    # kwargs["attempt"] = attempt
                    return await func(*args)
                except RateLimitError as e:
                    last_exception = e
                    retry_after = extract_retry_after(str(e)) or default_wait
                    retry_after = (
                        default_wait if retry_after > max_wait else retry_after
                    )  # Limit max wait time

                    if attempt < retries - 1:
                        logger.warning(
                            f"Rate limit hit. Retrying in {retry_after} seconds... (Attempt {attempt + 1}/{retries})"
                        )
                        await asyncio.sleep(retry_after)
                    else:
                        logger.error(f"Max retries reached. Raising error: {e}")
                        raise
                except APIConnectionError as e:
                    last_exception = e
                    logger.warning(
                        f"APIConnectionError, Retrying in {default_wait} seconds... (Attempt {attempt + 1}/{retries})"
                    )
                    await asyncio.sleep(default_wait)

                except Exception as e:
                    last_exception = e
                    logger.warning(
                        f"Exception, Retrying in {default_wait} seconds... (Attempt {attempt + 1}/{retries})"
                    )
                    logger.error(f"{traceback.format_exc()}")
                    await asyncio.sleep(default_wait)
            if last_exception:
                raise last_exception

        return wrapper

    return decorator


def extract_retry_after(error_message):
    """Extracts the 'retry after' time from the OpenAI error message."""
    match = re.search(r"retry after (\d+) seconds", error_message, re.IGNORECASE)
    return int(match.group(1)) if match else None
