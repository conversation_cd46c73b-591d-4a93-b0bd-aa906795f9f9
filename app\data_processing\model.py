import itertools
from enum import Enum
from typing import Union

import pandas as pd
import tiktoken
from azure.ai.documentintelligence.models import (
    DocumentFigure,
    DocumentParagraph,
    DocumentSection,
    DocumentTable,
)
from pydantic import BaseModel

from app.data_processing.utils import normalize_number


class FormatType(Enum):
    LATEX = "latex"
    HTML = "html"
    MARKDOWN = "markdown"


class ChunkType(Enum):
    TEXT = "text"
    TABLE = "table"
    FIGURE = "figure"


class Paragraph(DocumentParagraph):
    def __init__(self, id, parent=None, **kwargs):
        self.id = id
        self.parent = parent
        self.bounding_box: BoundingBox | None = None
        self.description = ""
        super().__init__(kwargs)

    def __repr__(self) -> str:
        return f"Paragraph(id={self.id})"


class Figure(DocumentFigure):
    def __init__(self, parent=None, **kwargs):
        # Call the superclass constructor with the necessary keyword arguments
        self.image = None
        self.parent = parent
        self.bounding_box: BoundingBox | None = None
        self.description: str | None = None
        self.details: str | None = None
        self.reconstructed = ""
        self.region = ""
        self.figure_name = ""
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        return f"Figure(id={self.id}, parent={self.parent})"

    @property
    def content(self) -> str:
        return self.description

    def get_description(self) -> None:
        return str(self.description)

    def get_details(self) -> None:
        return str(self.details)


class Table(DocumentTable):
    def __init__(
        self, id, page_width, page_height, page_rotation, parent=None, **kwargs
    ):
        self.id = id
        self.parent = parent
        self.bounding_box: BoundingBox | None = None
        self.description: str | None = ""
        self.reconstructed = ""
        self.region = ""
        self.table_name = ""
        self.attached_paragraph = ""
        self.page_width = page_width
        self.page_height = page_height
        self.page_rotation = page_rotation
        super().__init__(kwargs)

    def __repr__(self) -> str:
        return f"Table(id={self.id}, parent={self.parent})"

    @property
    def content(self) -> str:
        return self.description

    def restructure_table(self, format_type="latex") -> str:
        """
        Restructures the table by reconstructing the cell content into a specified format.

        Parameters:
        -----------
        format_type : FormatType
            The desired output format for the reconstructed table. Options are:
            - FormatType.LATEX: Returns the table in LaTeX format.
            - FormatType.HTML: Returns the table in HTML format.
            - FormatType.MARKDOWN: Returns the table in Markdown format.

        Returns:
        --------
        str
            The reconstructed table in the specified format.
        """
        cells = self.cells
        num_rows = max(cell["rowIndex"] + cell.get("rowSpan", 1) for cell in cells)
        num_cols = max(
            cell["columnIndex"] + cell.get("columnSpan", 1) for cell in cells
        )
        table_df = pd.DataFrame("", index=range(num_rows), columns=range(num_cols))
        for cell in cells:
            row_start = cell["rowIndex"]
            col_start = cell["columnIndex"]
            row_span = cell.get("rowSpan", 1)
            col_span = cell.get("columnSpan", 1)
            content = cell.get("content", "")
            norm = normalize_number(content)
            norm_content = norm if norm is not None else content
            for r, c in itertools.product(
                range(row_start, row_start + row_span),
                range(col_start, col_start + col_span),
            ):
                table_df.iat[r, c] = norm_content
        table_df.index = [""] * len(table_df.index)
        table_df.columns = [""] * len(table_df.columns)
        if format_type == FormatType.LATEX:
            reconstructed_table = table_df.to_latex(
                index=False, na_rep="", escape=False
            )
        elif format_type == FormatType.HTML:
            reconstructed_table = table_df.to_html()
        elif format_type == FormatType.MARKDOWN:
            reconstructed_table = table_df.to_markdown()
        else:
            raise ValueError(f"Unsupported format type: {format_type}")
        return reconstructed_table


class Section(DocumentSection):
    def __init__(self, id, parent=None, children=None, **kwargs):
        self.id = id
        self.parent = parent
        self.children = children or []
        super().__init__(kwargs)

    def __repr__(self) -> str:
        return f"Section(id={self.id}, parent={self.parent})"

    def add_child(self, child: Union[Paragraph, Figure, Table]) -> None:
        child.parent = self
        self.children.append(child)


class Chunk:
    def __init__(
        self,
        id: str = None,
        parent=None,
        metadata: str = "",
        encoding_name: str = "gpt-4",
    ) -> None:
        self.id = id
        self.parent = parent
        self.children: list[Paragraph | Table | Figure] = []
        self.level = 0  # support level, first version not needed
        self.content = []
        self.metadata = metadata
        self.encoding = tiktoken.encoding_for_model(encoding_name)

    def add_child(self, child: Paragraph | Table | Figure) -> None:
        if isinstance(child, Paragraph):
            self.children.append(child)
            self.content.append(child.content)
        elif isinstance(child, Table):
            self.children.append(child)
            self.content.append(child.reconstructed)
        elif isinstance(child, Figure):
            self.children.append(child)
            self.content.append(child.reconstructed)

    def get_content(self) -> str:
        return "\n".join(self.content)

    def get_length(self, token_count: bool = False) -> int:
        """Will be used to compute token length"""
        return 1 if token_count else len(self.get_content())

    def get_num_tokens(self) -> int:
        """Returns the number of tokens in a text string."""
        content = self.get_content()
        num_tokens = len(self.encoding.encode(content))
        return num_tokens

    def get_bounding_box(self) -> list[dict]:
        bounding_boxes = []
        for item in self.children:
            if hasattr(item, "bounding_box") and item.bounding_box is not None:
                bounding_boxes.append(item.bounding_box.dict())
        return bounding_boxes

    def __str__(self) -> str:
        return f"Chunk(id={self.id}, parent={self.parent})"

    def __repr__(self) -> str:
        return f"Chunk(id={self.id}, parent={self.parent})"


class IndexChunk(BaseModel):
    content: str
    metadata: dict = {}


class BoundingBox(BaseModel):
    x1: float
    x2: float
    y1: float
    y2: float
    width: float
    height: float
    pageNumber: int
    pageRotation: int


class DocumentTableCellMetadata(BaseModel):
    page_number: int
    row_index: int
    column_index: int
    content: str
    polygon: list[float]


class DocumentTableMetaData(BaseModel):
    table_id: str
    bounding_box: dict
    page_number: int
    polygon: list[float]
    cells: list[DocumentTableCellMetadata]
    column_count: int
    row_count: int
    reconstructed: str
