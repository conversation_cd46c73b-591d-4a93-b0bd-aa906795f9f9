from typing import List, <PERSON><PERSON>

from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from app.database.db import engine


async def get_sequences_from_db() -> List[Tuple[str, str]]:
    """Fetch sequences directly from PostgreSQL system catalogs"""
    query = """
    SELECT
        c.relname as table_name,
        s.relname as sequence_name
    FROM pg_class s
    JOIN pg_depend d ON d.objid = s.oid
    JOIN pg_class c ON d.refobjid = c.oid
    JOIN pg_namespace n ON n.oid = s.relnamespace
    WHERE s.relkind = 'S'
    AND n.nspname = 'public'
    ORDER BY table_name;
    """

    try:
        with engine.connect() as connection:
            cursor = connection.connection.cursor()
            cursor.execute(query)
            return cursor.fetchall()
    except SQLAlchemyError as e:
        logger.error(f"Failed to fetch sequences: {e}")
        return []


async def update_sequences():
    """Update all sequences based on their table's maximum ID"""
    sequences = await get_sequences_from_db()

    if not sequences:
        logger.warning("No sequences found in database")
        return

    with engine.connect() as connection:
        cursor = connection.connection.cursor()

        try:
            for table, sequence in sequences:
                increment = 0 if table in ["WaterMark", "WaterMarkDetail"] else 1

                query = f"""
                SELECT pg_catalog.setval(
                    '"{sequence}"',
                    COALESCE((SELECT MAX(id) + {increment} FROM "{table}"), 1),
                    true
                );
                """

                cursor.execute(query)
                result = cursor.fetchone()

                if result:
                    logger.info(
                        f"Updated {sequence} for table {table} to {result[0]}",
                    )
                else:
                    logger.warning(f"Failed to update sequence for {table}")

            connection.commit()
            logger.info("All sequences updated successfully")

        except Exception as e:
            logger.error(f"Error updating sequences: {e}")
            connection.rollback()
