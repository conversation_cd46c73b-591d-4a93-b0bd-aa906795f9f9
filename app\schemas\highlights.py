from typing import Any

from pydantic import BaseModel, Field

from app.data_processing.model import Table


class BoundingBox(BaseModel):
    x1: float
    x2: float
    y1: float
    y2: float
    width: float
    height: float
    page_number: int
    page_rotation: int

    def gen_id(self) -> str:
        """Generate a unique ID for the bounding box based on its coordinates and page number."""
        return f"{self.x1:.4f}-{self.y1:.4f}-{self.x2:.4f}-{self.y2:.4f}-{self.page_number}"


class HighlightCell(BaseModel):
    """The cell that should be highlighted to map to the item.

    Attributes:
        row_index (int): The row index of the cell.
        column_index (int): The column index of the cell.
        table_id (str): The id of the table.
        highlight_item_id (str): The id of the highlight item.

    For example:
        - You want to select the cell [1, 3] in table tables/1, then:
        ```python
        HighlightCell(row_index=1, column_index=3, table_id="tables/1")
        ```
    """

    row_index: int = Field(description="the row index of the cell")
    column_index: int = Field(description="the column index of the cell")
    table_id: str = Field(description="the id of the table")


class HighlightItem(BaseModel):
    """The item that should be highlighted to map to the cell.

    Attributes:
        highlight_item_id (str): The id of the highlight item.
        highlight_cells (list[HighlightCell]): The cells that should be highlighted to map to the item.

    Example:
        1. You want to highlight item 'current_assets' at cell [1, 3] in table tables/1, then:
        ```python
        HighlightItem(
            highlight_item_id="current_assets",
            highlight_cells=[HighlightCell(row_index=1, column_index=3, table_id="tables/1")]
        )
        ```
        2. You want to highlight item 'borrowings' at cell [2, 4] in table tables/2 and cell [3, 5] in table tables/3, then:
        ```python
        HighlightItem(
            highlight_item_id="borrowings",
            highlight_cells=[
                HighlightCell(row_index=2, column_index=4, table_id="tables/2"),
                HighlightCell(row_index=3, column_index=5, table_id="tables/3")
            ]
        )
        ```
    """

    highlight_item_id: str = Field(description="the id of the highlight item")
    highlight_cells: list[HighlightCell] = Field(
        description="the cells that should be highlighted to map to the item"
    )


class HighlightItems(BaseModel):
    """The list of items that should be highlighted to map to the cells.

    Attributes:
        highlight_items (list[HighlightItem]): The list of highlight items.
    """

    highlight_items: list[HighlightItem] = Field(
        description="the items that should be highlighted to map to the cells"
    )


class OutputItem(BaseModel):
    """A item representing a financial item."""

    id: str = Field(description="the id of the item")
    value: float = Field(description="the value of the item")
    extracted_from: str | None = Field(description="the note of the item")
    bounding_regions: list[BoundingBox | dict] | None = Field(
        description="the bounding regions of the item, by default, it should be list of HighlightCell"
    )

    def get_str(self) -> str:
        """
        Returns a string representation of the OutputItem in the same format as print_kv_value.

        Returns:
            A formatted string representation of the item
        """
        item_repr = f"<item>\n- id: {self.id}\n- value: {self.value}\n"
        if self.extracted_from:
            item_repr += f"- extracted from: {self.extracted_from}\n"
        item_repr += "</item>\n"
        return item_repr


class ExtractHighlightInput(BaseModel):
    input_instance: Any
    table_list: list[Table]
    prompt: str

    class Config:
        arbitrary_types_allowed = True
