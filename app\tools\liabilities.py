from langchain.tools import tool
from loguru import logger

from app.schemas.liabilities import (
    CurrentLiabilities,
    Liabilities,
    LiabilitiesAndStakeholdersEquity,
    LongTermLiabilities,
)


@tool
def check_liabilities_sum(
    liabilities_n_stakeholders_equity: LiabilitiesAndStakeholdersEquity,
) -> str:
    """Check the accuracy of extracted values for liabilities and stakeholders' equity.

    Args:
        liabilities (LiabilitiesAndStakeholdersEquity): The extracted values for liabilities and stakeholders' equity.
        The values should be extracted from balance sheet table only and be the end-of-period values.
    Return:
        str: the message indicates result of checking
    """

    logger.warning("Start check liabilities and stakeholders' equity sum")
    liabilities: Liabilities = liabilities_n_stakeholders_equity.liabilities
    current_liabilities_output: CurrentLiabilities = liabilities.current_liabilities
    long_term_liabilities_output: LongTermLiabilities = (
        liabilities.long_term_liabilities
    )
    return_response: str = ""

    # Check current liabilities
    current_liabilities_check = check_current_liabilities(current_liabilities_output)

    # Check long-term liabilities
    long_term_liabilities_check = check_long_term_liabilities(
        long_term_liabilities_output
    )

    # Check total liabilities
    current_liabilities_value = 0
    if current_liabilities_output and current_liabilities_output.current_liabilities:
        current_liabilities_value = current_liabilities_output.current_liabilities.value

    long_term_liabilities_value = 0
    if (
        long_term_liabilities_output
        and long_term_liabilities_output.long_term_liabilities
    ):
        long_term_liabilities_value = (
            long_term_liabilities_output.long_term_liabilities.value
        )

    total_liabilities_value = 0
    if liabilities.total_liabilities:
        total_liabilities_value = liabilities.total_liabilities.value

    # Check if sum of current and long-term liabilities equals total liabilities
    tolerance = 0.01
    if (
        abs(
            (current_liabilities_value + long_term_liabilities_value)
            - total_liabilities_value
        )
        <= tolerance
    ):
        return_response += f"\nThe total liabilities value is good ({current_liabilities_value + long_term_liabilities_value} vs {total_liabilities_value}). -> validated."
    elif (
        current_liabilities_value + long_term_liabilities_value
    ) < total_liabilities_value:
        return_response += "\nThe total liabilities value (extracted from financial report) is greater than the sum of current and long-term liabilities. Please check these fields again, you might be missing some values."
    else:
        return_response += "\nThe total liabilities value (extracted from financial report) is less than the sum of current and long-term liabilities. Please check these fields again, you might have duplicated values."

    output = return_response + "\n".join(
        [current_liabilities_check, long_term_liabilities_check]
    )
    logger.warning(f"Output of validation tool:\n {output}")
    return output


def check_current_liabilities(current_liabilities: CurrentLiabilities) -> str:
    """Return message after checking sum for current liabilities values"""
    logger.debug("Checking current liabilities")
    if not current_liabilities or not current_liabilities.current_liabilities:
        return "No current liabilities data available to check."

    # Calculate sum of all current liabilities components
    component_sum = 0
    components = []

    if current_liabilities.trade_notes_payable:
        component_sum += current_liabilities.trade_notes_payable.value
        components.append(
            f"Trade Notes Payable: {current_liabilities.trade_notes_payable.value}"
        )

    if current_liabilities.trade_accounts_payable:
        component_sum += current_liabilities.trade_accounts_payable.value
        components.append(
            f"Trade Accounts Payable: {current_liabilities.trade_accounts_payable.value}"
        )

    if current_liabilities.trade_payables_to_related_companies:
        component_sum += current_liabilities.trade_payables_to_related_companies.value
        components.append(
            f"Trade Payables to Related Companies: {current_liabilities.trade_payables_to_related_companies.value}"
        )

    if current_liabilities.short_term_loans_payable:
        component_sum += current_liabilities.short_term_loans_payable.value
        components.append(
            f"Short-term Loans Payable: {current_liabilities.short_term_loans_payable.value}"
        )

    if current_liabilities.short_term_loans_payable_to_related_companies:
        component_sum += (
            current_liabilities.short_term_loans_payable_to_related_companies.value
        )
        components.append(
            f"Short-term Loans Payable to Related Companies: {current_liabilities.short_term_loans_payable_to_related_companies.value}"
        )

    if current_liabilities.current_portion_of_long_term_borrowings:
        component_sum += (
            current_liabilities.current_portion_of_long_term_borrowings.value
        )
        components.append(
            f"Current Portion of Long-term Borrowings: {current_liabilities.current_portion_of_long_term_borrowings.value}"
        )

    if current_liabilities.lease_obligations:
        component_sum += current_liabilities.lease_obligations.value
        components.append(
            f"Lease Obligations: {current_liabilities.lease_obligations.value}"
        )

    if current_liabilities.a_p_accrued_expense:
        component_sum += current_liabilities.a_p_accrued_expense.value
        components.append(
            f"A/P & Accrued Expense: {current_liabilities.a_p_accrued_expense.value}"
        )

    if current_liabilities.income_tax_payable:
        component_sum += current_liabilities.income_tax_payable.value
        components.append(
            f"Income Tax Payable: {current_liabilities.income_tax_payable.value}"
        )

    if current_liabilities.dividends_payable:
        component_sum += current_liabilities.dividends_payable.value
        components.append(
            f"Dividends Payable: {current_liabilities.dividends_payable.value}"
        )

    if current_liabilities.a_r_unearned_revenue:
        component_sum += current_liabilities.a_r_unearned_revenue.value
        components.append(
            f"A/R & Unearned Revenue: {current_liabilities.a_r_unearned_revenue.value}"
        )

    if current_liabilities.other_allowances:
        component_sum += current_liabilities.other_allowances.value
        components.append(
            f"Other Allowances: {current_liabilities.other_allowances.value}"
        )

    if current_liabilities.deferred_tax_liabilities:
        component_sum += current_liabilities.deferred_tax_liabilities.value
        components.append(
            f"Deferred Tax Liabilities: {current_liabilities.deferred_tax_liabilities.value}"
        )

    if current_liabilities.other_current_liabilities:
        component_sum += current_liabilities.other_current_liabilities.value
        components.append(
            f"Other Current Liabilities: {current_liabilities.other_current_liabilities.value}"
        )

    total_value = current_liabilities.current_liabilities.value

    message = "\nChecking current liabilities:\n"
    if components:
        message += "Components:\n- " + "\n- ".join(components) + "\n"
    else:
        message += "No specific current liabilities components found.\n"

    message += f"Sum of components: {component_sum}\n"
    message += f"Total current liabilities: {total_value}\n"

    # Tolerance for floating point comparison
    tolerance = 0.01
    if abs(component_sum - total_value) <= tolerance:
        message += "The current liabilities sum is correct."
    elif component_sum < total_value:
        message += f"Warning: The sum of current liabilities components ({component_sum}) is less than the total ({total_value}). Some components may be missing or undervalued."
    else:
        message += f"Warning: The sum of current liabilities components ({component_sum}) is greater than the total ({total_value}). Some components may be duplicated or overvalued."

    logger.debug("Finish checking current liabilities")
    return message


def check_long_term_liabilities(long_term_liabilities: LongTermLiabilities) -> str:
    """Return message after checking sum for long-term liabilities values"""
    logger.debug("Checking long-term liabilities")
    if not long_term_liabilities or not long_term_liabilities.long_term_liabilities:
        return "No long-term liabilities data available to check."

    # Calculate sum of all long-term liabilities components
    component_sum = 0
    components = []

    if long_term_liabilities.bonds_debentures:
        component_sum += long_term_liabilities.bonds_debentures.value
        components.append(
            f"Bonds & Debentures: {long_term_liabilities.bonds_debentures.value}"
        )

    if long_term_liabilities.long_term_loans_payable:
        component_sum += long_term_liabilities.long_term_loans_payable.value
        components.append(
            f"Long-term Loans Payable: {long_term_liabilities.long_term_loans_payable.value}"
        )

    if long_term_liabilities.long_term_loans_payable_to_related_companies:
        component_sum += (
            long_term_liabilities.long_term_loans_payable_to_related_companies.value
        )
        components.append(
            f"Long-term Loans Payable to Related Companies: {long_term_liabilities.long_term_loans_payable_to_related_companies.value}"
        )

    if long_term_liabilities.lease_obligations:
        component_sum += long_term_liabilities.lease_obligations.value
        components.append(
            f"Lease Obligations: {long_term_liabilities.lease_obligations.value}"
        )

    if long_term_liabilities.long_term_other_accounts_payable:
        component_sum += long_term_liabilities.long_term_other_accounts_payable.value
        components.append(
            f"Long-term Other Accounts Payable: {long_term_liabilities.long_term_other_accounts_payable.value}"
        )

    if long_term_liabilities.long_term_deposits:
        component_sum += long_term_liabilities.long_term_deposits.value
        components.append(
            f"Long-term Deposits: {long_term_liabilities.long_term_deposits.value}"
        )

    if long_term_liabilities.retirement_benefits_obligations:
        component_sum += long_term_liabilities.retirement_benefits_obligations.value
        components.append(
            f"Retirement Benefits Obligations: {long_term_liabilities.retirement_benefits_obligations.value}"
        )

    if long_term_liabilities.other_allowances:
        component_sum += long_term_liabilities.other_allowances.value
        components.append(
            f"Other Allowances: {long_term_liabilities.other_allowances.value}"
        )

    if long_term_liabilities.deferred_tax_liabilities:
        component_sum += long_term_liabilities.deferred_tax_liabilities.value
        components.append(
            f"Deferred Tax Liabilities: {long_term_liabilities.deferred_tax_liabilities.value}"
        )

    if long_term_liabilities.other_long_term_liabilities:
        component_sum += long_term_liabilities.other_long_term_liabilities.value
        components.append(
            f"Other Long-term Liabilities: {long_term_liabilities.other_long_term_liabilities.value}"
        )

    total_value = long_term_liabilities.long_term_liabilities.value

    message = "\nChecking long-term liabilities:\n"
    if components:
        message += "Components:\n- " + "\n- ".join(components) + "\n"
    else:
        message += "No specific long-term liabilities components found.\n"

    message += f"Sum of components: {component_sum}\n"
    message += f"Total long-term liabilities: {total_value}\n"

    # Tolerance for floating point comparison
    tolerance = 0.01
    if abs(component_sum - total_value) <= tolerance:
        message += "The long-term liabilities sum is correct."
    elif component_sum < total_value:
        message += f"Warning: The sum of long-term liabilities components ({component_sum}) is less than the total ({total_value}). Some components may be missing or undervalued."
    else:
        message += f"Warning: The sum of long-term liabilities components ({component_sum}) is greater than the total ({total_value}). Some components may be duplicated or overvalued."

    logger.debug("Finish checking long-term liabilities")
    return message
