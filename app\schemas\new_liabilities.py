from pydantic import BaseModel, Field

from .base import NewItem


class CurrentLiabilities(BaseModel):
    """A class representing current liabilities in a financial statement.

    Attributes:
        trade_notes_payable (NewItem | None): Short-term promissory notes issued to vendors for goods or services purchased.
        trade_accounts_payable (NewItem | None): Total amount payable to external third parties for goods or services received but not yet paid. This includes: Trade payables to third parties and Accrued customer incentives (if listed under trade payables)
        trade_payables_related_companies (NewItem | None): Refer to amounts payable to affiliates, subsidiaries, or related parties for goods or services received. This value usually appears under the "Trade payables" section.
        short_term_loans_payable (NewItem | None): Borrowings that are due within one year, typically obtained from banks or financial institutions. Exclude any amounts explicitly labeled as "lease liabilities", "lease obligations", “trust receipts”, “vendor payables”, or similar terms, as those should be mapped to lease_obligations_current. Focus on lines that mention short-term loans, bank overdrafts, revolving credit, or current portion of interest-bearing borrowings unrelated to leases.
        st_loans_payable_related (NewItem | None): Short-term borrowings from affiliated or related companies due within one year.
        current_portion_long_term_loans (NewItem | None): The portion of long-term loans or borrowings that is due for repayment within the next 12 months. Do not map to this if the source label clearly refers to: Lease obligations, Lease liabilities
        current_portion_bonds (NewItem | None): Portion of bonds payable that becomes due within the next year.
        lease_obligations_current (NewItem | None): Current portion of lease obligations due within one year.
        other_accounts_payable (NewItem | None): Other short-term debts not classified as trade payables or loans.
        accrued_expense (NewItem | None): Expenses that have been incurred but not yet paid, and are explicitly labeled as accrued expenses or equivalent (e.g., “Accrued Expenses”, “Accrued Operating Expenses”, “Provision for Expenses”). Only extract this value if it appears directly in the financial statement or can be deterministically computed from clearly defined line items (e.g., “Creditors for Expenses”, “Statutory Liabilities”) with no ambiguity. Do not infer or approximate values that are not explicitly reported or computable.
        accrued_consumption_taxes (NewItem | None): Consumption or sales taxes collected but not yet remitted to tax authorities.
        income_tax_payable (NewItem | None): Taxes owed to the government based on taxable income but not yet paid.
        dividends_payable (NewItem | None): Dividends declared but not yet distributed to shareholders.
        advances_received (NewItem | None): Payments received directly from customers before goods or services have been delivered.  These are typically labeled as “Advance from Customers”, “Customer Deposits”, or similar. Do not include: Funds received from banks or financial institutions, even if linked to receivables or trade debtor arrangements. Cash advances facilitated through factoring, securitization, or invoice-based financing, which should instead be mapped to unearned_revenue.
        deposits_received (NewItem | None): Money held temporarily that may need to be returned or applied later.
        unearned_revenue (NewItem | None): This refers to amounts received in advance for goods or services that have not yet been delivered or performed. These amounts are recorded as liabilities until the company fulfills its obligations. Common labels include “Advance from Customers”, “Customer Advances”, and “Deferred Revenue”. You should also include cases where the company receives cash in advance from a bank or financial institution, provided it is explicitly linked to trade receivables or customer collections. For example, lines like “Amount transferred from the bank in advance under the cash collected from the trade debtors arrangement” should be included. Be careful not to count the same item more than once, even if it appears multiple times with identical descriptions or amounts.
        allowance_for_bonuses (NewItem | None): Estimated amounts set aside for employee bonuses to be paid in the near future.
        deferred_tax_liabilities_current (NewItem | None): Tax liabilities that will come due within the next year.
        derivatives_current (NewItem | None): Short-term financial instruments with negative values, due within one year.
        other_current_liabilities_operating (NewItem | None): Other short-term liabilities related to operating activities. Do not include: “Amounts due to related parties” (should be mapped to trade_payables_related_companies)
        other_current_liabilities_financing (NewItem | None): Other short-term liabilities related to financing activities. Do not include: “Amounts due to related parties” (should be mapped to trade_payables_related_companies)
        total_current_liabilities_extracted (NewItem | None): Sum of all obligations due within one year of the balance sheet date.
    """

    trade_notes_payable: NewItem | None = Field(
        description="Short-term promissory notes issued to vendors for goods or services purchased. Commonly labeled 'Trade Notes Payable' or 'Notes Payable'. Typically appears under 'Current Liabilities', often grouped with trade payables. Excludes accounts payable and bank borrowings."
    )

    trade_accounts_payable: NewItem | None = Field(
        description="Amounts owed to suppliers for goods or services received but not yet paid."
    )

    trade_payables_related_companies: NewItem | None = Field(
        description="Payables to affiliates or related companies for goods or services. May be labeled as 'Amounts Due to Related Parties' or similar. Found under 'Trade Payables' or in related party disclosures."
    )

    short_term_loans_payable: NewItem | None = Field(
        description="Borrowings from banks or other creditors due within one year. Labels include 'Short-term Loans', 'Bank Loans', 'Working Capital Loans'. Excludes lease liabilities. Found under 'Current Liabilities'."
    )

    st_loans_payable_related: NewItem | None = Field(
        description="Short-term borrowings from shareholders or related companies. Labeled as 'Loan from Shareholder' or 'Loan from Related Company'. Appears under 'Current Liabilities' or related party notes."
    )

    current_portion_long_term_loans: NewItem | None = Field(
        description="Portion of long-term loans due within one year. Often labeled 'Current portion of employee benefit obligations' or 'Current Portion of Long-term Loans'. Found under 'Current Liabilities'. Excludes lease-related items and short-term borrowings."
    )

    current_portion_bonds: NewItem | None = Field(
        description="Portion of bonds due within the next year. Commonly labeled 'Current Portion of Bonds Payable'. Appears under 'Current Liabilities'. Excludes long-term or convertible bonds not maturing soon."
    )

    lease_obligations_current: NewItem | None = Field(
        description="Lease liabilities due within one year. Labeled as 'Lease Obligations'. Found under 'Current Liabilities'. Only extract if explicitly labeled. Default as 'None'"
    )

    other_accounts_payable: NewItem | None = Field(
        description="Non-trade, non-loan payables. Labels include 'Other Payables' or 'Miscellaneous Payables'. Appears under 'Current Liabilities'. Excludes accrued expenses, trade payables, and loans."
    )

    accrued_expense: NewItem | None = Field(
        description="Expenses incurred but not yet paid. Labeled as 'Accrued Expenses' or similar. Only extract if clearly labeled or explicitly computed. Found under 'Current Liabilities'."
    )

    accrued_consumption_taxes: NewItem | None = Field(
        description="VAT or sales tax collected but not yet paid. Labels include 'VAT Payable' or 'Sales Tax Payable'. Found in the tax section under 'Current Liabilities'."
    )

    income_tax_payable: NewItem | None = Field(
        description="Taxes owed on income but not yet paid. Common labels: 'Income Tax Payable', 'Corporate Tax Payable'. Excludes deferred taxes and VAT. Found under 'Current Liabilities'."
    )

    dividends_payable: NewItem | None = Field(
        description="Declared but unpaid dividends to shareholders. Labeled as 'Dividends Payable' or 'Unpaid Dividends'. Found under 'Current Liabilities'."
    )

    advances_received: NewItem | None = Field(
        description="Customer payments received in advance for goods or services. Labeled 'Advance from Customers' or 'Customer Deposits'. Excludes bank advances or factoring (map those to unearned_revenue). Found under 'Current Liabilities'."
    )

    deposits_received: NewItem | None = Field(
        description="Funds held temporarily from customers or third parties, often refundable. Labeled 'Deposits Received', 'Security Deposits'. May be classified as current or non-current depending on the term."
    )

    unearned_revenue: NewItem | None = Field(
        description="Revenue received in advance for undelivered goods or services. Labels include 'Deferred Revenue', 'Customer Advances'. Includes advances tied to receivables arrangements. Found under 'Current Liabilities'."
    )

    allowance_for_bonuses: NewItem | None = Field(
        description="Estimated bonuses payable to employees. Labeled 'Provision for Bonuses' or 'Bonus Accrual'. Found under 'Other Current Liabilities'."
    )

    other_allowances_current: NewItem | None = Field(
        description="Short-term provisions for expenses expected within a year. Labels include 'Other Current Provisions' or 'Provision for Restructuring', or 'Provision for Income Tax'. Excludes accrued operating costs."
    )

    deferred_tax_liabilities_current: NewItem | None = Field(
        description="Deferred tax liabilities expected to settle within one year. Labeled 'Current Deferred Tax Liabilities'. Excludes long-term tax deferrals. Found under 'Current Liabilities' or tax notes."
    )

    derivatives_current: NewItem | None = Field(
        description="Negative fair value of derivative instruments due within a year. Labeled as 'Derivative Liabilities' or 'Fair Value of Derivatives'. Found in 'Financial Liabilities'."
    )

    other_current_liabilities_operating: NewItem | None = Field(
        description="Operating-related current liabilities not classified elsewhere. Labeled as 'Other Current Liabilities', 'Other Payables – Operating' or similar. Excludes related party or financing obligations."
    )

    other_current_liabilities_financing: NewItem | None = Field(
        description="Short-term financing-related liabilities, like interest or fees. Labeled as 'Finance Charges Payable'. Excludes operating or related party liabilities"
    )

    total_current_liabilities_extracted: NewItem | None = Field(
        description="Sum of all current liabilities due within one year. Labeled as 'Total Current Liabilities' or similar. Appears at the end of the current liabilities section on the balance sheet."
    )


class LongTermLiabilities(BaseModel):
    """A class representing long-term liabilities in a financial statement.

    Attributes:
        bonds_debentures (NewItem | None): Corporate bonds and similar debt securities with maturity dates beyond one year.
        long_term_loans_payable (NewItem | None): Borrowings from financial institutions or other lenders that are classified as non-current liabilities — i.e., amounts not due within the next 12 months. Include: Line items labeled "Borrowings" or "Loans" under the **Non-current liabilities** section, Any bank or financial debt explicitly marked as long-term.
        lt_loans_payable_related (NewItem | None): Long-term borrowings from affiliated or related companies due beyond one year.
        long_term_accounts_payable (NewItem | None): Amounts owed to suppliers due after one year from the balance sheet date.
        long_term_deposits (NewItem | None): Deposits received that are not expected to be returned within one year.
        lease_obligations_lt (NewItem | None): Long-term portion of lease obligations due beyond one year.
        retirement_benefits_obligations (NewItem | None): Estimated future costs of employee retirement benefits.
        other_allowances_lt (NewItem | None): Other estimated future expenses expected to be paid after one year.
        deferred_tax_liabilities_lt (NewItem | None): Tax liabilities that will come due after one year.
        derivatives_lt (NewItem | None): Long-term financial instruments with negative values, due beyond one year.
        other_lt_liabilities_operating (NewItem | None): Other long-term liabilities related to operating activities not classified elsewhere.
        other_lt_liabilities_investing (NewItem | None): Other long-term liabilities related to investing activities not classified elsewhere.
        other_lt_liabilities_financing (NewItem | None): Other long-term liabilities related to financing activities not classified elsewhere.
        total_long_term_liabilities_extracted (NewItem | None): Sum of all obligations due beyond one year of the balance sheet date.
    """

    bonds_debentures: NewItem | None = Field(
        description="Corporate bonds, debentures, or similar debt securities with maturity dates beyond one year. Typically labeled as 'Bonds Payable', 'Debentures', 'Corporate Bonds', or 'Medium-Term Notes'. Appears under the 'Non-current Liabilities' section of the balance sheet. Excludes short-term bonds and bank loans."
    )

    long_term_loans_payable: NewItem | None = Field(
        description="Borrowings from banks, financial institutions, or other lenders that are due after 12 months. Typical labels include 'Long-term Loans', 'Non-current Borrowings', or 'Longterm Payable'. Do not map items that contain lease-related terms such as 'Lease Liabilities', 'Lease Obligations', or 'Right-of-Use Liabilities' — those must be mapped to lease_obligations_lt."
    )

    lt_loans_payable_related: NewItem | None = Field(
        description="Long-term loans payable to affiliated or related companies, such as a parent or shareholder, due beyond one year. Commonly labeled as 'Loan from Parent Company', 'Related Party Loan', or 'Shareholder Loan'. Appears in 'Non-current Liabilities' or footnotes under related party disclosures."
    )

    long_term_accounts_payable: NewItem | None = Field(
        description="Trade payables due after more than one year, often structured through deferred payment agreements. May appear as 'Accounts Payable – Long-term' or 'Deferred Payables'. Found under 'Non-current Liabilities' and less frequently reported."
    )

    long_term_deposits: NewItem | None = Field(
        description="Deposits or security deposits held for more than one year, such as long-term rental or customer deposits. Typically labeled 'Long-term Deposits', 'Security Deposits (Non-current)'. Found under 'Non-current Liabilities'; excludes deposits in the assets section."
    )

    lease_obligations_lt: NewItem | None = Field(
        description="Lease liabilities due beyond one year under finance or operating leases. Must include the term 'lease', such as 'Non-current Lease Liabilities', 'Lease Obligations (Long-term)', or 'Right-of-Use Liabilities'. Exclude any borrowings that are not explicitly labeled with lease-related terms — those should be mapped to long_term_loans_payable. Default as 'None' if ambiguity exists."
    )

    retirement_benefits_obligations: NewItem | None = Field(
        description="Future obligations for employee retirement plans such as pensions or post-employment benefits. Labels include 'Retirement Benefit Obligations', 'Pension Liabilities', and 'Defined Benefit Plans'. Appears in 'Non-current Liabilities' or 'Provisions'."
    )

    other_allowances_lt: NewItem | None = Field(
        description="Long-term provisions for future obligations like legal liabilities, warranties, or restructuring costs. Commonly labeled as 'Provisions for Legal Claims', 'Warranty Reserves', or 'Restructuring Provisions'. Found under 'Non-current Liabilities'."
    )

    deferred_tax_liabilities_lt: NewItem | None = Field(
        description="Deferred tax obligations expected to arise beyond one year. Typically labeled 'Deferred Tax Liabilities (Non-current)' or 'Long-term Deferred Taxes'. Located in 'Non-current Liabilities', sometimes near or within the tax section."
    )

    derivatives_lt: NewItem | None = Field(
        description="Long-term liabilities representing the negative fair value of derivative contracts maturing beyond one year. Common labels include 'Derivative Liabilities (Non-current)' or 'Hedging Instruments – Liabilities (LT)'. Listed under 'Non-current Liabilities' or financial liabilities."
    )

    other_lt_liabilities_operating: NewItem | None = Field(
        description="Other long-term liabilities related to core operations that are not classified elsewhere. Commonly labeled 'Other Operating Liabilities (Non-current)' or 'Miscellaneous Operating Liabilities'. Found under 'Non-current Liabilities', often grouped as 'Others'."
    )

    other_lt_liabilities_investing: NewItem | None = Field(
        description="Long-term liabilities linked to investing activities, such as deferred payments for capital expenditures. Typical labels: 'Payables for Asset Purchases', 'Deferred Acquisition Liabilities'. Appears under 'Non-current Liabilities'."
    )

    other_lt_liabilities_financing: NewItem | None = Field(
        description="Long-term liabilities associated with financing activities not categorized under loans or bonds. Labels include 'Other Financial Liabilities (Non-current)' or 'Financing Payables'. Found under 'Non-current Liabilities', usually with financial obligations."
    )

    total_long_term_liabilities_extracted: NewItem | None = Field(
        description="Sum of all liabilities due beyond the next 12 months. Typically labeled 'Total Non-current Liabilities' or 'Total Long-term Liabilities'. Found at the end of the 'Non-current Liabilities' section of the balance sheet."
    )


class NetAssets(BaseModel):
    """A class representing net assets in a financial statement.
    These include equity and other components that contribute to the net worth of a company.

    Attributes:
        capital_stock (NewItem | None): Par value of shares issued to shareholders, representing ownership in the company.
        capital_surplus (NewItem | None): Amount paid by shareholders in excess of the par value of shares.
        retained_earnings (NewItem | None): Refers to the cumulative amount of net income that a company has retained, rather than distributed as dividends to shareholders. It includes both appropriated earnings (such as legal reserves) and unappropriated earnings (which are available for distribution or reinvestment)
        treasury_stock (NewItem | None): Company shares that have been repurchased from shareholders.
        other_shareholders_equity (NewItem | None): Includes equity components not classified under share capital or retained earnings. This typically includes items such as: “Hedging reserve”, “Amalgamation reserve” or “Reserve on amalgamation”. You should add or subtract these values as presented in the equity section, taking care to preserve their signs (positive or negative) exactly as shown in the table. Avoid rounding or reinterpreting the sign of each component.
        total_net_assets_extracted (NewItem | None): Sum of all shareholders' equity accounts.
        minority_interests (NewItem | None): Portion of subsidiary income belonging to minority shareholders.
        total_shareholders_equity_extracted (NewItem | None): Total equity attributable to shareholders, including capital stock, capital surplus, retained earnings, and other components.
        unrealized_gain_loss_on_securities (NewItem | None): Unrealized gains or losses on securities held by the company, which are not yet realized through sale or transfer.
        unrealized_gain_loss_on_derivatives (NewItem | None): Unrealized gains or losses on derivative instruments held by the company, which are not yet realized through settlement.
        foreign_currency_translation_adjustments (NewItem | None): Adjustments made to the financial statements due to fluctuations in foreign currency exchange rates.
        valuation_translation_investing_cf (NewItem | None): Valuation adjustments related to investing cash flows.
        valuation_translation_operating_cf (NewItem | None): Valuation adjustments related to operating cash flows.
        total_other_comprehensive_income_extracted (NewItem | None): Total of all unrealized gains and losses that are excluded from net income but included in shareholders’ equity.
        equity_attributable_owners_parent_extracted (NewItem | None): The portion of equity that is attributable to the owners of the parent company, excluding minority interests.
        other_non_recurring_loss (NewItem | None): Non-recurring losses that are not part of the regular business operations and are not expected to occur again in the future.
    """

    capital_stock: NewItem | None = Field(
        description="""
        Represents the par value of issued shares — ownership capital directly contributed by shareholders.
        [1] Nature of the value:
        Reflects the par value of issued shares, excluding capital surplus or additional paid-in capital.
        This is the original contributed capital from shareholders.
        [2] Identification (Labels/Format):
        Commonly labeled as “Capital Stock”, “Common Stock”, “Share Capital”, or “Issued Capital”.
        Typically found under the “Equity” or “Net Assets” section on the balance sheet.
        [3] Distinction from Similar Fields:
        Does not include capital surplus (amount above par), treasury stock (repurchased shares), or retained earnings.
        Only represents the par value of issued shares, excluding unissued shares or stock options.
        [4] Typical Location on Balance Sheet:
        Usually appears near the top of the “Equity” or “Net Assets” section on the balance sheet.
        """
    )

    capital_surplus: NewItem | None = Field(
        description="""
        Amount received from shareholders above the par value of shares, often recorded during share issuance.
        [1] Nature of the value:
        Represents additional paid-in capital beyond the par value of shares issued.
        Includes premiums from share issuance, excluding retained earnings or other reserves.
        [2] Identification (Labels/Format):
        Commonly labeled as “Capital Surplus”, “Additional Paid-in Capital”, or “Share Premium”.
        Usually shown in the equity section below capital stock.
        [3] Distinction from Similar Fields:
        Distinct from retained earnings and capital stock.
        Does not include treasury stock or other equity reserves.
        [4] Typical Location on Balance Sheet:
        Typically located directly below capital stock in the equity section.
        """
    )

    retained_earnings: NewItem | None = Field(
        description="""
        Cumulative net income retained by the company after dividend distributions, including both appropriated and unappropriated earnings.
        [1] Nature of the value:
        Reflects accumulated profits reinvested in the business, excluding unrealized gains or capital reserves.
        [2] Identification (Labels/Format):
        Common labels include “Retained Earnings”, “Earned Surplus”, or “Total Reserve & Surplus”.
        Found within shareholders’ equity or net assets.
        [3] Distinction from Similar Fields:
        Excludes unrealized gains/losses (Other Comprehensive Income), capital reserves, or equity adjustments.
        [4] Typical Location on Balance Sheet:
        Listed under “Shareholders’ Equity” or “Net Assets” section.
        """
    )

    treasury_stock: NewItem | None = Field(
        description="""
        Value of shares repurchased and held by the company.
        [1] Nature of the value:
        Represents the cost of treasury shares, typically shown as a negative amount reducing equity.
        [2] Identification (Labels/Format):
        Commonly labeled as “Treasury Stock” or “Own Shares”.
        [3] Distinction from Similar Fields:
        Distinct from capital stock and capital surplus.
        Usually reported as a negative figure within equity.
        [4] Typical Location on Balance Sheet:
        Appears near the bottom of the equity section, reducing total equity.
        """
    )

    other_shareholders_equity: NewItem | None = Field(
        description="""
        Equity components not classified under share capital, surplus, or retained earnings, including various reserves like hedging reserve or amalgamation reserve.
        [1] Nature of the value:
        Represents equity reserves and adjustments other than core capital and retained earnings.
        [2] Identification (Labels/Format):
        Often labeled as “Other Reserves”, “Reserve on Amalgamation”, “Hedging Reserve”.
        Sign (positive or negative) should be preserved as shown.
        [3] Distinction from Similar Fields:
        Excludes Other Comprehensive Income (OCI) items (which map to total_other_comprehensive_income_extracted) and core capital items (capital_stock, capital_surplus).
        [4] Typical Location on Balance Sheet:
        Grouped with equity reserves in the net assets section.
        """
    )

    total_net_assets_extracted: NewItem | None = Field(
        description="""
        Total equity or net worth after all liabilities are accounted for.
        [1] Nature of the value:
        Represents the comprehensive net assets of the company available to shareholders.
        [2] Identification (Labels/Format):
        Commonly labeled as “Total Net Assets” or “Net Worth”.
        [3] Distinction from Similar Fields:
        Avoid extracting if individual equity components are already included separately to prevent duplication.
        [4] Typical Location on Balance Sheet:
        Usually the final line of the equity or net assets section.
        """
    )

    minority_interests: NewItem | None = Field(
        description="""
        Portion of net assets belonging to non-controlling shareholders in subsidiaries.
        [1] Nature of the value:
        Represents equity interest held by minority shareholders, distinct from parent shareholders.
        [2] Identification (Labels/Format):
        Labeled as “Minority Interests” or “Non-controlling Interests”.
        [3] Distinction from Similar Fields:
        Excludes equity attributable to the parent company.
        [4] Typical Location on Balance Sheet:
        Shown below total shareholders’ equity.
        """
    )

    total_shareholders_equity_extracted: NewItem | None = Field(
        description="""
        Total equity attributable to shareholders including all components.
        [1] Nature of the value:
        Represents aggregate shareholders’ equity, inclusive of capital stock, surplus, retained earnings, and other reserves.
        [2] Identification (Labels/Format):
        Labels include “Total Shareholders’ Equity” or “Total Equity Attributable to Owners”.
        [3] Distinction from Similar Fields:
        Should not be extracted if components are already reported individually to avoid double counting.
        [4] Typical Location on Balance Sheet:
        Usually located just before minority interests.
        """
    )

    unrealized_gain_loss_on_securities: NewItem | None = Field(
        description="""
        Unrealized fair value adjustments on marketable securities not yet sold.
        [1] Nature of the value:
        Reflects mark-to-market valuation changes that have not been realized through sale.
        [2] Identification (Labels/Format):
        Common labels include “Unrealized Gain/Loss on AFS Securities”, “Fair Value Adjustment – Securities”.
        [3] Distinction from Similar Fields:
        Excludes realized gains/losses or trading income.
        [4] Typical Location on Balance Sheet:
        Found within “Other Comprehensive Income” under equity.
        """
    )

    unrealized_gain_loss_on_derivatives: NewItem | None = Field(
        description="""
        Unrealized gains or losses on derivative financial instruments.
        [1] Nature of the value:
        Reflects mark-to-market adjustments on derivatives not yet settled.
        [2] Identification (Labels/Format):
        Labeled as “Hedging Reserve” or “Derivatives Fair Value Adjustment”.
        [3] Distinction from Similar Fields:
        Excludes interest-bearing or realized derivative instruments.
        [4] Typical Location on Balance Sheet:
        Included within “Other Comprehensive Income” in equity.
        """
    )

    foreign_currency_translation_adjustments: NewItem | None = Field(
        description="""
        Equity changes resulting from translation of foreign subsidiaries’ financial statements into the reporting currency.
        [1] Nature of the value:
        Reflects exchange differences arising from consolidation of foreign operations.
        [2] Identification (Labels/Format):
        Labeled as “Foreign Currency Translation Adjustment” or “Exchange Differences on Translation”.
        [3] Distinction from Similar Fields:
        Excludes foreign exchange gains or losses from trading activities.
        [4] Typical Location on Balance Sheet:
        Typically included within “Other Comprehensive Income”.
        """
    )

    valuation_translation_investing_cf: NewItem | None = Field(
        description="""
        Fair value or currency translation adjustments related to investing activities.
        [1] Nature of the value:
        Reflects FX or valuation changes tied specifically to investing cash flows.
        [2] Identification (Labels/Format):
        Labeled as “Valuation Difference – Investing CF” or “FX Adjustment on Investing”.
        [3] Distinction from Similar Fields:
        Does not include operational or financing impacts.
        [4] Typical Location on Balance Sheet:
        Rarely presented on balance sheets; usually found in cash flow statement notes.
        """
    )

    valuation_translation_operating_cf: NewItem | None = Field(
        description="""
        Adjustments in operating cash flow due to foreign exchange or valuation effects.
        [1] Nature of the value:
        Reflects FX or valuation impacts specifically related to operating activities.
        [2] Identification (Labels/Format):
        Labeled as “FX Impact on Operating Activities” or “Valuation Adjustment – Operating CF”.
        [3] Distinction from Similar Fields:
        Excludes investing or financing activity adjustments.
        [4] Typical Location on Balance Sheet:
        Generally found in cash flow statement reconciliations or notes.
        """
    )

    total_other_comprehensive_income_extracted: NewItem | None = Field(
        description="""
        Sum of all accumulated Other Comprehensive Income (OCI) items not included in net income.
        [1] Nature of the value:
        Represents cumulative OCI components such as unrealized gains/losses on securities, FX translation, derivatives, etc.
        [2] Identification (Labels/Format):
        Often labeled as “Total OCI” or “Other Components of Equity”.
        [3] Distinction from Similar Fields:
        Avoid extracting individual OCI items again if total OCI is reported.
        [4] Typical Location on Balance Sheet:
        Found in the equity section, usually near or under retained earnings.
        """
    )

    equity_attributable_owners_parent_extracted: NewItem | None = Field(
        description="""
        Portion of equity attributable to the parent company’s shareholders.
        [1] Nature of the value:
        Represents controlling interest equity, excluding minority interests.
        [2] Identification (Labels/Format):
        Labeled as “Equity Attributable to Owners of Parent” or “Controlling Interest”.
        [3] Distinction from Similar Fields:
        Excludes minority (non-controlling) interests.
        [4] Typical Location on Balance Sheet:
        Usually appears before or adjacent to minority interests.
        """
    )

    other_non_recurring_loss: NewItem | None = Field(
        description="""
        One-time or extraordinary losses not related to regular operations.
        [1] Nature of the value:
        Includes exceptional or non-recurring expenses, excluding routine impairments or operating costs.
        [2] Identification (Labels/Format):
        Labeled as “Exceptional Items”, “Extraordinary Loss”, or “Non-recurring Expense”.
        [3] Distinction from Similar Fields:
        Should not be confused with regular impairments or operating expenses.
        [4] Typical Location on Financial Statements:
        Usually disclosed in the income statement or accompanying footnotes.
        """
    )


class LiabilitiesAndNetAssets(BaseModel):
    """A class representing liabilities and net assets in a financial statement.

    Attributes:
        current_liabilities (CurrentLiabilities): Short-term obligations due within one year.
        long_term_liabilities (LongTermLiabilities): Long-term obligations due beyond one year.
        net_assets (NetAssets): Shareholders' equity and other components of company's net worth.
        total_liabilities_and_net_assets_extracted (float): Total of all liabilities and net assets, which should equal total assets.
        total_liabilities_extracted (float): The sum of total current liabilities and total long-term liabilities.
    """

    current_liabilities: CurrentLiabilities = Field(
        description="Short-term obligations that are expected to be settled within one year."
    )
    long_term_liabilities: LongTermLiabilities = Field(
        description="Long-term obligations that are due beyond one year from the balance sheet date."
    )
    net_assets: NetAssets = Field(
        description="Shareholders' equity and other components that represent the company's net worth."
    )
    total_liabilities_and_net_assets_extracted: float = Field(
        description="Total of all liabilities and net assets, which should equal total assets on the balance sheet."
    )
    total_liabilities_extracted: float = Field(
        description="The sum of total current liabilities and total long-term liabilities."
    )
