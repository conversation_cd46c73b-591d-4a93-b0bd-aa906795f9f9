from typing import <PERSON><PERSON>, Union

from azure.ai.documentintelligence.models import AnalyzeResult

from app.data_processing.model import (
    BoundingBox,
    Figure,
    FormatType,
    Paragraph,
    Section,
    Table,
)

INCH_TO_PIXEL = 96


class CustomTextSplitter:
    def __init__(self, **kwargs) -> None:
        self.kwargs = kwargs
        self.section_list: list[Section] = []
        self.table_list: list[Table] = []
        self.figure_list: list[Figure] = []
        self.paragraph_list: list[Paragraph] = []
        self.document_dict: dict[str, Union[Section, Paragraph]] = {}
        self.analyze_result = None
        self.page_rotation = 0
        self.section_parents = {}
        self.paragraph_section_headings = {}

    @staticmethod
    def parse_input_string(input_string: str) -> tuple[str, int]:
        parts = input_string.split("/")
        content_type = parts[1]
        number = parts[2]
        return content_type, int(number)

    def handle_all_section(self, idx):
        """First step to chunking the document based on the section

        Args:
            idx (str): the id of the section to be processed
        """
        if idx in self.document_dict.keys():
            return
        _, _id = self.parse_input_string(idx)
        section = self.analyze_result.sections[_id]
        section_parent = self.section_parents.get(str(_id), {})
        sect = Section(id=idx, parent=section_parent, **section.__dict__["_data"])
        self.document_dict[idx] = sect
        self.section_list.append(sect)

        current_title_dict = {"title": "", "heading": "", "content": ""}
        children: list[str] = section.elements

        prev_child_id = children[0]
        prev_paragraph_content = ""
        for _, child_id in enumerate(children):
            if child_id.startswith("/paragraphs/"):
                _, idx = self.parse_input_string(child_id)
                data = self.analyze_result.paragraphs[idx].__dict__["_data"]
                para = Paragraph(
                    id=f"/paragraphs/{idx}",
                    parent=None,
                    **data,
                )
                para.description = "text"
                sect.add_child(para)
                self.paragraph_list.append(para)
                self.document_dict[para.id] = para

                current_title_dict = self.update_titles(child_id, current_title_dict)

                prev_paragraph_content = data["content"]
            elif child_id.startswith("/sections/"):
                self.handle_all_section(child_id)
                child = self.document_dict[child_id]
                sect.add_child(child)
            elif child_id.startswith("/tables/"):
                if prev_child_id.startswith("/paragraphs/"):
                    self.handle_table(
                        child_id, current_title_dict, sect, prev_paragraph_content
                    )
                else:
                    self.handle_table(child_id, current_title_dict, sect)
            elif child_id.startswith("/figures/"):
                self.handle_figure(child_id, section)
            prev_child_id = child_id

    def _extract_parent_sections(self):
        # Extract sections' parent section and its heading title {"section_id": {"parent": "section_id", "heading": "first_paragraph"}}
        section_parents = {}
        paragraph_parent_section_headings = {}
        if self.analyze_result.sections:
            for index, section in enumerate(self.analyze_result.sections):
                heading_title = ""
                if section.elements and section.elements[0].startswith("/paragraphs/"):
                    heading_title = self.analyze_result.paragraphs[
                        int(section.elements[0].split("/")[-1])
                    ].content
                # Get all child section of the current section
                for element in section.elements:
                    if element.startswith("/sections/"):
                        section_parents[element.split("/")[-1]] = {
                            "parent": index,
                            "heading": heading_title,
                        }
                    elif element.startswith("/paragraphs/"):
                        para_id = element.split("/")[-1]
                        paragraph_parent_section_headings[para_id] = heading_title

            self.section_parents = section_parents
            self.paragraph_section_headings = paragraph_parent_section_headings

    async def construct_data(
        self, analyze_result: AnalyzeResult, page_rotation: int
    ) -> list[Table]:
        """Construct data from the analysis result, including:
        - Chunking the document based on sections
        - Convert and crop file into images to process figures

        Args:
            analyze_result (AnalyzeResult): AnalyzeResult object
        """
        self.analyze_result = analyze_result
        self.page_rotation = page_rotation
        self._extract_parent_sections()
        self.handle_all_section("/sections/0")
        self._process_bounding_regions()

        return self.table_list

    def _process_bounding_regions(self) -> None:
        """Extract bounding regions for paragraphs, tables, and figure"""
        for para in self.paragraph_list:
            self._get_coordinate(paragraph=para)
        for table in self.table_list:
            self._get_coordinate(paragraph=table)

    def _get_coordinate(self, paragraph: Union[Paragraph, Table, Figure]) -> None:
        if len(paragraph.bounding_regions) > 1 and isinstance(
            paragraph, Table
        ):  # Hot fix for table have "caption"
            points = paragraph.bounding_regions[-1].polygon
        else:
            points = paragraph.bounding_regions[
                0
            ].polygon  # TODO: Add logic for the list has multiple bounding_regions
        x1, y1 = points[0], points[1]
        x2, y2 = points[4], points[5]

        first_page = self.analyze_result.pages[0].get("pageNumber") - 1
        page_number = paragraph.bounding_regions[0].page_number
        page_width, page_height, page_rotation = self._get_width_height_rotation(
            page_index=page_number - 1 - first_page
        )

        paragraph.bounding_box = BoundingBox(
            x1=x1 * INCH_TO_PIXEL,
            x2=x2 * INCH_TO_PIXEL,
            y1=y1 * INCH_TO_PIXEL,
            y2=y2 * INCH_TO_PIXEL,
            width=page_width * INCH_TO_PIXEL,
            height=page_height * INCH_TO_PIXEL,
            pageNumber=page_number,
            pageRotation=page_rotation,
        )

        return paragraph.bounding_box

    def update_titles(
        self, child_id: str, current_title_dict: dict, theshold_content: int = 800
    ) -> dict:
        _, idx = self.parse_input_string(child_id)
        para = self.analyze_result.paragraphs[idx]
        if para.role == "title":
            current_title_dict["title"] = para.content
        elif para.role == "sectionHeading":
            current_title_dict["heading"] = para.content
        elif para.role is None:
            current_title_dict["content"] = (
                para.content if len(para.content) < theshold_content else ""
            )
        return current_title_dict

    def handle_table(
        self,
        child_id: str,
        titles_dict: dict,
        section: Section,
        attached_paragraph_data: str = "",
    ) -> None:
        """
        Handle processing of table nodes and their description

        Args:
            child_id (str): ID of the table node.
            idx_children (int): Index of the current child in the list.
            children (list): List of all children in the current context.
            window_size (int): Number of surrounding elements to include in the window.
            section (Section): Section to which the table will be added.
            attached_paragraph_data: if any, it contains the content of the previous paragraph which might be table's contents, descriptions.
        """
        _, idx = self.parse_input_string(child_id)
        page_width, page_height, page_rotation = self._get_width_height_rotation(
            page_index=self.analyze_result.tables[idx].bounding_regions[0].page_number
            - 1
        )
        table = Table(
            id=f"/tables/{idx}",
            parent=None,
            page_width=page_width,
            page_height=page_height,
            page_rotation=page_rotation,
            **self.analyze_result.tables[idx].__dict__["_data"],
        )
        if table._data.get("caption"):
            caption = table._data.get("caption").content
        else:
            caption = ""
        table.reconstructed = table.restructure_table(FormatType.MARKDOWN)
        table.region = ""
        table.attached_paragraph = attached_paragraph_data
        table.description = f"Table ID: /tables/{idx}\nSection: {section.parent.get('heading', '')} \n {attached_paragraph_data + ':'} \nTitle: {titles_dict['title']} {caption}. \nFinancial table content: \n{table.reconstructed}."
        self.table_list.append(table)
        section.add_child(table)
        self.document_dict[table.id] = table

    def handle_figure(self, child_id: str, section: Section) -> None:
        """
        Handle processing of figure nodes, including extracting regions and generating descriptions.
        Args:
            child_id (str): ID of the figure node.
            section (Section): Section to which the figure will be added.
        """
        # Parse the child_id to get the index
        _, idx = self.parse_input_string(child_id)
        # Update the figure ID in the analyze result
        self.analyze_result.figures[idx].__dict__["_data"]["id"] = f"/figures/{idx}"
        figure_obj = self.analyze_result.figures[idx]

        if "boundingRegions" in figure_obj:
            figure_obj["bounding_regions"] = figure_obj.pop("boundingRegions")

        figure_data = getattr(figure_obj, "_data", None)
        # Create a Figure object
        figure = Figure(
            parent=None,
            **figure_data,
        )

        # Process figure elements if available
        if figure.elements:
            regions = []
            for id_child in figure.elements:
                id_child = int(id_child.split("/")[-1])  # Get id element
                region = self.analyze_result.paragraphs[
                    id_child
                ]  # Get region (paragraph) content
                if region:
                    regions.append(region.content)  # Append region content to the list

            figure.region = "\n".join(
                regions
            )  # Combine the regions into a single string

            # Add figure to section and update tracking lists
            self.figure_list.append(figure)
            self.document_dict[figure.id] = figure

    def _get_width_height_rotation(self, page_index: int) -> Tuple[float, float, int]:
        """
        page_index: start from 0
        """
        width, height, rotation = (
            self.analyze_result.pages[page_index].width,
            self.analyze_result.pages[page_index].height,
            self.page_rotation[page_index],
        )
        return width, height, rotation
