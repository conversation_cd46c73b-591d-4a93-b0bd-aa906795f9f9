from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.responses import JSONResponse
from loguru import logger
from sqlalchemy.orm import Session

from app.database.db import get_db
from app.features.conversation.model import FinancialDocument
from app.settings import AzureBlobConfig
from app.utils.azure_blob_util import generate_blob_sas_url

from .model import DocumentResponse

router = APIRouter()


@router.get("/{document_id}")
def get_document_by_id(
    request: Request,
    document_id: int,
    db: Session = Depends(get_db),
):
    """
    Get document by ID.
    """
    try:
        document = (
            db.query(FinancialDocument)
            .filter(FinancialDocument.id == document_id)
            .first()
        )
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )
        expired_time = datetime.utcnow() + timedelta(minutes=15)
        document_sas_token: str = (
            generate_blob_sas_url(document.blob_path, AzureBlobConfig(), expired_time)
            if document.blob_path
            else None
        )
        return DocumentResponse(
            id=document_id,
            sas_url=document_sas_token,
            filename=document.filename,
        )

    except Exception as e:
        logger.error(f"Error getting document by ID: {e}")
        return JSONResponse(
            content={"error": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
