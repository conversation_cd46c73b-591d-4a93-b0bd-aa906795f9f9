# Chatbot CLI Guide

This guide explains how to use the Chatbot command-line interface (CLI) to manage database migrations and server operations.

## Prerequisites

Ensure you have the following:
- Python installed
- PostgreSQL or another compatible database set up
- Necessary Python packages installed (e.g., SQLAlchemy, Alembic, Uvicorn, Click)

## Setup

1. **Install dependencies:**

2. **Configure your database settings** in the `app/settings.py` file.
Steps (with clean database):
- To create initial alembic version: python -m app.cli database revision -m 'init'
- Get the initial version ID and put it into cli.py/init_database()
- To create initial tables:
    - python -m app.cli database init
    - python -m app.cli database revision --autogenerate -m 'init tables'
    - python -m app.cli database upgrade --revision head
## Commands

### General Usage

Use the CLI script `app/cli.py` to interact with the Chatbot server and database. Add `--help` to any command for more information.

### Database Commands

#### Initialize the Database

Create the database schema and initialize Alembic for version control.

```sh
python -m app.cli database revision -m 'init'
```

#### Create a New Migration Revision

Create a new database migration script.

##### Autogenerate a Revision

```sh
python -m app.cli database revision --autogenerate -m 'init'
```

##### Manual Revision

```sh
python -m app.cli database revision -m 'add new table'
```

#### Apply Migrations

Upgrade the database schema to the latest version or a specific revision.

```sh
python -m app.cli database upgrade --revision head
```

#### Downgrade Migrations

Downgrade the database schema to a previous version.

```sh
python -m app.cli database downgrade --revision -1
```

### Server Commands

#### Start the Server

Start the Uvicorn server.

```sh
python -m app.cli server start
```

#### Start the Server in Development Mode

Start the Uvicorn server with live reload enabled.

```sh
python -m app.cli server develop
```

## Dump and Load Data

### Dump All Data

Dump all data from the database to a JSON file.

```sh
python -m app.cli database dump --output data.json
```

### Dump Specific Data

Dump specific data from a table to a JSON file.

```sh
python -m app.cli database dump_column <table_name> <column_name> --output data.json
```

### Load Data

Load data from a JSON file into the database.

```sh
python -m app.cli database load --input data.json

# Use **--force** to overwrite existing data in the database.
```

### Load Specific Data

Load specific data from a JSON file into the database.

```sh
python -m app.cli database load_column <table_name> <column_name> --input data.json

# Use **--force** to overwrite existing data in the database.
```

## Examples

### Initialize the Database

```sh
python -m app.cli database init
```

### Create a New Migration Revision

```sh
python -m app.cli database revision --autogenerate -m 'initial migration'
```

### Apply Migrations

```sh
python -m app.cli database upgrade --revision head
```

### Downgrade Migrations

```sh
python -m app.cli database downgrade --revision -1
```

## Troubleshooting

For help with commands:

```sh
python -m app.cli --help
python -m app.cli database --help
python -m app.cli server --help
```

Check logs or console output for detailed error messages.

---

By following this guide, you can manage your database migrations and server operations smoothly using the provided CLI.