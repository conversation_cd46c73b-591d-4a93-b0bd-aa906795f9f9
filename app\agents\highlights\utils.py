from typing import Any

import pandas as pd
from azure.ai.documentintelligence.models import DocumentTable
from loguru import logger

from app.data_processing.model import Table
from app.schemas.base import Item, NewItem
from app.schemas.highlights import (
    BoundingBox,
    HighlightCell,
    HighlightItems,
    OutputItem,
)
from app.utils.helper import convert_bounding_boxes_to_highlight_format


def transform_to_output_item(
    component: Any, prefix: str = "", separator: str = "/"
) -> list[OutputItem]:
    output_items = []

    if component is None:
        return output_items

    if hasattr(component, "__dict__"):
        component_dict = component.__dict__
    elif isinstance(component, dict):
        component_dict = component
    else:
        try:
            component_dict = dict(component)
        except (TypeError, ValueError):
            return output_items

    for key, value in component_dict.items():
        if key.startswith("_") or value is None:
            continue

        current_id = key if not prefix else f"{prefix}{separator}{key}"

        # ✅ Handle NewItem / Item
        if isinstance(value, (Item, NewItem)):
            item_value = 0.0
            note = None

            try:
                item_value = float(value.value) if value.value is not None else 0.0
                note = getattr(value, "note", None)
            except Exception as e:
                logger.warning(
                    f"[transform_to_output_item] Error reading 'value' for {current_id}: {e}"
                )

            output_items.append(
                OutputItem(
                    id=current_id,
                    value=item_value,
                    extracted_from=note,
                    bounding_regions=[],
                )
            )

        elif isinstance(value, (float, int)):
            output_items.append(
                OutputItem(
                    id=current_id, value=value, extracted_from=None, bounding_regions=[]
                )
            )

        elif hasattr(value, "__dict__") or isinstance(value, dict):
            output_items.extend(
                transform_to_output_item(value, prefix=current_id, separator=separator)
            )

        elif isinstance(value, list):
            for i, item in enumerate(value):
                nested_items = transform_to_output_item(
                    item, prefix=f"{current_id}{separator}{i}", separator=separator
                )
                output_items.extend(nested_items)

    return output_items


def convert_highlight_cells_to_bounding_boxes(
    highlight_cells: list[HighlightCell],
    table_list: list[Table],
) -> list[BoundingBox]:
    """
    Convert a HighlightItem to a list of BoundingBox objects by looking up cell coordinates
    in the provided table list.

    Args:
        highlight_item: The HighlightItem containing highlight cells
        table_list: List of tables with cell information

    Returns:
        List of BoundingBox objects representing the highlighted cells
    """
    bounding_boxes = []

    for highlight_cell in highlight_cells:
        # Extract table index from table_id (format: "tables/N")
        table_id_parts = highlight_cell.table_id.split("/")
        if len(table_id_parts) < 2:
            continue

        table_index = int(table_id_parts[-1])

        # Ensure table index is valid
        if table_index >= len(table_list):
            continue

        table: Table = table_list[table_index]

        # Find matching cell in the table
        matching_cells = [
            cell
            for cell in table.cells
            if (
                cell.row_index == highlight_cell.row_index
                and cell.column_index == highlight_cell.column_index
            )
        ]

        if not matching_cells:
            continue

        cell = matching_cells[0]
        if hasattr(cell, "bounding_regions") and cell.bounding_regions:
            polygon = cell.bounding_regions[0].polygon
            if polygon:
                # Assuming polygon is a list of coordinates
                bounding_box = BoundingBox(
                    x1=polygon[0],
                    y1=polygon[1],
                    x2=polygon[4],
                    y2=polygon[5],
                    width=table.page_width,
                    height=table.page_height,
                    page_rotation=table.page_rotation,
                    page_number=cell.bounding_regions[0].page_number,
                )
                bounding_boxes.append(bounding_box)

    return bounding_boxes


def merge_bounding_regions(
    output_items: list[OutputItem],
    highlight_items: HighlightItems,
    table_list: list[Table | DocumentTable],
) -> list[OutputItem]:
    """
    Merge bounding regions from highlight_items into matching output_items based on ID.

    Args:
        output_items: List of OutputItem objects
        highlight_items: HighlightItems instance containing highlight cells

    Returns:
        Updated list of OutputItem objects with bounding_regions filled in
    """
    # Create a dictionary for faster lookup of output_items by id
    output_item_dict = {item.id: item for item in output_items}

    # Create an ID mapping to handle potential format differences
    # This assumes that highlight_item_id might be just the leaf name without the prefix
    id_mapping = {}
    for item_id in output_item_dict.keys():
        # Extract the leaf name (after the last separator)
        if "/" in item_id:
            leaf_name = item_id.split("/")[-1]
            id_mapping[leaf_name] = item_id
        else:
            id_mapping[item_id] = item_id

    # Merge bounding regions
    if highlight_items.highlight_items:
        for highlight_item in highlight_items.highlight_items:
            highlight_id = highlight_item.highlight_item_id
            highlight_cells = highlight_item.highlight_cells

            # Try to find a match in our output items
            matching_id = None

            # Direct match
            if highlight_id in output_item_dict:
                matching_id = highlight_id

            # Match by leaf name
            elif highlight_id in id_mapping:
                matching_id = id_mapping[highlight_id]

            # If we found a match, merge the bounding regions
            if matching_id and highlight_cells:
                output_item_dict[matching_id].bounding_regions = (
                    convert_bounding_boxes_to_highlight_format(
                        convert_highlight_cells_to_bounding_boxes(
                            highlight_cells, table_list=table_list
                        )
                    )
                )

    # Return the updated list
    return list(output_item_dict.values())


def generate_table_cells_repr(table: Table) -> str:
    output = "<table>\n"
    output += f"Table id: {table.id}\n"
    for cell in table.cells:
        if cell.content and cell.content not in [":unselected:"]:
            output += f" - Cell [{cell.row_index}, {cell.column_index}], content: {cell.content}\n"
    return output + "</table>\n"


def convert_to_dataframe(income_output_items: list[OutputItem]):
    """table containing id, value, and 'extracted_from' columns"""
    data = []
    for item in income_output_items:
        data.append(
            {
                "id": item.id.split("/")[-1],
                "value": item.value,
                "extracted_from": item.extracted_from,
            }
        )
    df = pd.DataFrame(data)
    return df
