SYSTEM_PROMPT = """
You are a precise and detail-oriented assistant for financial statement analysis.

Your task is to analyze a collection of financial tables and determine:
1. Which tables are part of the company’s **Balance Sheet**
2. Which tables are **detail tables** that support specific balance sheet line items

---

### What is a Balance Sheet?

A balance sheet shows a company's financial position at a specific point in time. It includes:

1. **Assets** – What the company owns
2. **Liabilities** – What the company owes
3. **Equity** – The residual interest after liabilities are deducted from assets

---

### Balance Sheet Table Identification Criteria

A table qualifies as a **balance sheet** if it includes a **structured set of at least two of the three components**: assets, liabilities, and equity.

Include the following as balance sheet tables:
- Complete balance sheet tables
- Tables split by section (e.g., one for assets, one for liabilities/equity)
- Continuation tables (e.g., “Balance Sheet (continued)”)
- Tables in any language, as long as the structure and key elements are recognizable

---

### Detail Table Identification Rules

Detail tables explain or break down specific balance sheet line items. These must be grouped into **exactly 9 predefined categories**, which are organized under their corresponding balance sheet components:

#### **ASSETS**
- `"inventories"`: raw materials, work in progress, finished goods, merchandise, etc.
- `"tangible_fixed_assets"`: land, buildings, machinery, equipment, accumulated depreciation, etc.
- `"intangible_assets"`: goodwill, software, licenses, patents, trademarks, etc.
- `"investment_assets"`: financial assets, subsidiaries, joint ventures, equity investments, etc.
- `"other_assets"`: deferred tax assets, prepayments, receivable breakdowns, miscellaneous assets, etc.

#### **LIABILITIES**
- `"current_liabilities"`: trade payables, accrued expenses, short-term borrowings, etc.
- `"long_term_liabilities"`: bonds payable, long-term loans, lease liabilities, pension obligations, etc.

#### **EQUITY**
- `"equity"`: common stock, share capital, retained earnings, reserves, etc.
- `"other_comprehensive_income"`: currency translation adjustments, unrealized gains/losses on investments, actuarial gains/losses, etc.

---

### Detail Table Classification Methods

You may classify a table as a detail table using:

#### A. Reference-Based Linking (Explicit)
- The balance sheet includes note references (e.g., “Note 10”, “(22)”) or a “Note” column
- A detail table has a matching title (e.g., “Note 10: Investments”)

#### B. Semantic Matching (Implicit)
Even if no explicit note is present, classify a table as a detail table if it includes fields that are **subcomponents** of one of the above 9 categories.

For example:
- A table with `short-term borrowings` maps to `current_liabilities`
- A table with `patents` and `software` maps to `intangible_assets`
- A table with `currency translation adjustments` maps to `other_comprehensive_income`

---

### What to Exclude

Exclude any table that is:
- A profit and loss (income) statement
- A cash flow statement
- A notes-only section with no structured numeric data
- A schedule or footnote with only text

---

### Input Format

You will receive a list of tables in Markdown. Each table includes:
- A `Table ID`
- A `Section`
- A `Title`
- A `Table Content` (tabular data)

---

### Output Requirements

Your job is to assign each valid table to:

1. `balance_sheet_ids` – a list of table IDs that represent primary balance sheet tables
2. `detail_table_ids` – a dictionary with **exactly these 9 fixed keys**:
   - `inventories`
   - `current_assets`
   - `tangible_fixed_assets`
   - `intangible_assets`
   - `investment_assets`
   - `fixed_assets`
   - `current_liabilities`
   - `long_term_liabilities`
   - `equity`
   - `other_comprehensive_income`

Each key should map to a list of table IDs that correspond to that detail category.

Return only this structured output. Do not include explanations or summaries.
"""


HUMAN_PROMPT = """
Here are the definition of the templates:
Balance sheet is a financial statement that provides a snapshot of a company's financial position at a specific point in time, showing what it owns (assets), what it owes (liabilities), and the owners' equity (also known as net worth). It's one of the core financial statements, along with the income statement and cash flow statement.

Income statement, also known as a profit and loss (P&L) statement or earnings statement, summarizes a company's revenue, expenses, and net income or loss over a specific period, typically a month, quarter, or year. It's one of the three primary financial statements, along with the balance sheet and cash flow statement.

Please determine the most appropriate template for the following item names.
Extracted item name:
{{asset_name}}
"""

batch_classify_prompt = """
Here is the input:

{table_markdown}

Your task is to classify each table into:
1. `balance_sheet_ids`: Table IDs of primary balance sheet tables
2. `detail_table_ids`: A dictionary with exactly the 9 predefined keys that map to specific balance sheet components.

---

### Output Format

Respond in **valid JSON** using this structure:

```json
{{
  "balance_sheet_ids": ["..."],
  "detail_table_ids": {{
    "inventories": ["..."],
    "current_assets": ["..."],
    "tangible_fixed_assets": ["..."],
    "intangible_assets": ["..."],
    "investment_assets": ["..."],
    "fixed_assets": ["..."],
    "current_liabilities": ["..."],
    "long_term_liabilities": ["..."],
    "equity": ["..."],
    "other_comprehensive_income": ["..."]
  }}
}}
```

---

### Output Rules
1. Only include valid Table IDs from the input
2. Use empty lists ([]) for categories with no matching tables
3. Each table should be assigned to only one category
4. Do not include explanations, summaries, or notes — only return the JSON structure
"""

balance_sheet_items = [
    "Cash & Bank Deposits",
    "Trade Notes Receivable",
    "Trade Accounts Receivable",
    "Trade Receivables from Related Companies",
    "Lease Receivable & Investment Assets",
    "Securities",
    "Inventories",
    "Advances to Suppliers",
    "Prepaid Expense",
    "Other Accounts Receivable & Accrued Income",
    "Short-term Loans Receivable",
    "ST Loans Receivable from Related Co",
    "Deferred Tax Assets",
    "Other Current Assets",
    "Allowance for Doubtful Accounts",
    "Trade Accounts Payable",
    "Trade Payables to Related Companies",
]


income_statement_items = [
    "Gross Sales",
    "Cost of Sales",
    "Gross Profit",
    "Selling, General & Administrative Expenses",
    "Operating Income",
    "Interest Income",
    "Dividend Received",
    "Equity-Method Affiliate Profits",
    "Foreign Exchange Gains",
    "Gain on Sales of Non-Current Assets",
    "Gain on Sales of Investment & Securities",
    "Gain from Asset Revaluation",
    "Other Extraordinary Income",
    "Non-Recurring Income",
    "Interest Expenses",
    "Equity-Method Affiliate Losses",
    "Foreign Exchange Losses",
    "Loss on Sales & Disposal of Non-Current Assets",
    "Loss on Sales of Investment & Securities",
    "Loss from Asset Revaluation",
    "Other Extraordinary Loss",
    "Non-Recurring Loss",
    "Non-Recurring Items (before Tax)",
    "Net Income before Tax",
    "Income Taxes",
    "Net Income before Minority Interest",
    "Minority Interest",
    "Discontinued Operation",
    "Extraordinary Items (after Tax)",
    "Net Income",
]
