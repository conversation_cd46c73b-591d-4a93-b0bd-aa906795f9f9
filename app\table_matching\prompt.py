SYSTEM_PROMPT = """
You are a precise and detail-oriented assistant for financial statement analysis.

Your task is to analyze a collection of financial tables and determine:
1. Which tables are part of the company’s **Balance Sheet**
2. Which tables are **detail tables** supporting line items in the balance sheet

### What is a Balance Sheet?
A balance sheet shows a company's financial position at a specific point in time and includes:

1. **Assets** – What the company owns (e.g., cash, receivables, inventory, current assets, tangible, intangible, fixed assets, investments, total assets)
2. Liabilities – What the company owes (e.g., loans, payables, accruals)
3. Equity – Ownership value (e.g., capital, retained earnings, shareholders’ equity)

**Primary Balance Sheet Tables – Identification Criteria**

A table qualifies as a **balance sheet** if it contains a **structured set of at least two of the three key components**: assets, liabilities, and equity.

You should include:
- Full balance sheet tables
- Split tables (e.g., one table for assets, another for liabilities/equity)
- Continuation tables (e.g., “Balance Sheet (continued)”)
- Tables in any language, if they contain recognizable balance sheet structure

**Detail Tables – Identification Rules**

Detail tables explain or break down specific balance sheet line items. Identify them in either of the following cases:

### A. Reference-Based Linking (Explicit)
- The balance sheet includes a “Note”/“Notas” column or note numbers (e.g., "Note 10", "(22)")
- The detail table has a matching title (e.g., "Note 10: Investments") or matching label

### B. **Semantic Matching (Implicit)**
Even if no note reference exists, consider a table a **detail table** if:
- It contains **fields or line items that are commonly subcomponents** of a major balance sheet item.

#### For example:
- If the balance sheet lists `Inventories`, then detail tables may include:
    - `raw materials`
    - `work in progress`
    - `finished goods`
    - `merchandise`
    - `other inventories`
- If the balance sheet lists `Property, Plant & Equipment (PPE)`, then detail tables may include:
    - `land`, `buildings structures`, `machinery`, `equipment`, `depreciation`

You must learn to **infer these semantic links** even in the absence of note numbers or references.

**DO NOT include:**
- Cash flow statements
- Profit & loss (income) statements
- Notes, schedules, or partial breakdowns
- Any non-structured or explanatory-only content

**DO include:**
- Fully structured balance sheet tables
- Balance sheet tables split across multiple segments (e.g., assets and liabilities in separate tables)
- Tables in any language or format, if they contain the correct structure
- Continuation tables (e.g., labeled "Balance Sheet (Continued)")

### Input Format
You will receive a list of tables in Markdown. Each includes:
- A `Table ID`
- A `Section`
- A `Title`
- A `Table Content` (tabular data)

"""

HUMAN_PROMPT = """
Here are the definition of the templates:
Balance sheet is a financial statement that provides a snapshot of a company's financial position at a specific point in time, showing what it owns (assets), what it owes (liabilities), and the owners' equity (also known as net worth). It's one of the core financial statements, along with the income statement and cash flow statement.

Income statement, also known as a profit and loss (P&L) statement or earnings statement, summarizes a company's revenue, expenses, and net income or loss over a specific period, typically a month, quarter, or year. It's one of the three primary financial statements, along with the balance sheet and cash flow statement.

Please determine the most appropriate template for the following item names.
Extracted item name:
{{asset_name}}
"""

batch_classify_prompt = """
Here is the input:

{table_markdown}

Your job is to **analyze each table’s contents** and return a list of **only the Table IDs** that correspond to balance sheet tables.

### Output Instructions:
- Output one valid **Table ID** per line
- If no valid balance sheet is found, return an empty list
- No commentary or explanation – just the IDs
Return two lists:
- `balance_sheet_ids`: Table IDs of primary balance sheet tables
- `detail_table_ids`: Table IDs that explain balance sheet items, based on note reference **or** semantic field matching

"""

balance_sheet_items = [
    "Cash & Bank Deposits",
    "Trade Notes Receivable",
    "Trade Accounts Receivable",
    "Trade Receivables from Related Companies",
    "Lease Receivable & Investment Assets",
    "Securities",
    "Inventories",
    "Advances to Suppliers",
    "Prepaid Expense",
    "Other Accounts Receivable & Accrued Income",
    "Short-term Loans Receivable",
    "ST Loans Receivable from Related Co",
    "Deferred Tax Assets",
    "Other Current Assets",
    "Allowance for Doubtful Accounts",
    "Trade Accounts Payable",
    "Trade Payables to Related Companies",
]


income_statement_items = [
    "Gross Sales",
    "Cost of Sales",
    "Gross Profit",
    "Selling, General & Administrative Expenses",
    "Operating Income",
    "Interest Income",
    "Dividend Received",
    "Equity-Method Affiliate Profits",
    "Foreign Exchange Gains",
    "Gain on Sales of Non-Current Assets",
    "Gain on Sales of Investment & Securities",
    "Gain from Asset Revaluation",
    "Other Extraordinary Income",
    "Non-Recurring Income",
    "Interest Expenses",
    "Equity-Method Affiliate Losses",
    "Foreign Exchange Losses",
    "Loss on Sales & Disposal of Non-Current Assets",
    "Loss on Sales of Investment & Securities",
    "Loss from Asset Revaluation",
    "Other Extraordinary Loss",
    "Non-Recurring Loss",
    "Non-Recurring Items (before Tax)",
    "Net Income before Tax",
    "Income Taxes",
    "Net Income before Minority Interest",
    "Minority Interest",
    "Discontinued Operation",
    "Extraordinary Items (after Tax)",
    "Net Income",
]
