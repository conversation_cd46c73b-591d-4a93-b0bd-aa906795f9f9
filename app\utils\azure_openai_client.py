from langchain_openai import AzureChatOpenAI
from langchain_openai.embeddings import AzureOpenAIEmbeddings
from langfuse.callback import CallbackHandler

from app.settings import (
    AzureOpenAI,
    AzureOpenAIEmbeddingConfig,
    FastAzureOpenAI,
    LangfuseCallback,
)

azure_openai_chat_model = AzureChatOpenAI(
    model=AzureOpenAI.model_name,
    api_key=AzureOpenAI.api_key,
    azure_deployment=AzureOpenAI.azure_deployment,
    temperature=0,
    seed=0,
    api_version=AzureOpenAI.api_version,
    azure_endpoint=AzureOpenAI.azure_endpoint,
)

azure_openai_light_model = AzureChatOpenAI(
    model=FastAzureOpenAI.model_name,
    api_key=FastAzureOpenAI.api_key,
    azure_deployment=FastAzureOpenAI.azure_deployment,
    temperature=0.0,
    seed=0,
    api_version=FastAzureOpenAI.api_version,
    azure_endpoint=FastAzureOpenAI.azure_endpoint,
)

embedding_model = AzureOpenAIEmbeddings(
    azure_endpoint=AzureOpenAIEmbeddingConfig.azure_endpoint,
    openai_api_key=AzureOpenAIEmbeddingConfig.api_key,
    deployment=AzureOpenAIEmbeddingConfig.azure_deployment,
    chunk_size=AzureOpenAIEmbeddingConfig.chunk_size,
)

langfuse_handler = None

if LangfuseCallback.secret_key:
    langfuse_handler = CallbackHandler(
        public_key=LangfuseCallback.public_key,
        secret_key=LangfuseCallback.secret_key,
        host=LangfuseCallback.host,
    )
