"""add operator to FinancialConversation

Revision ID: 7f2a41338d41
Revises: 2f4e8294d927
Create Date: 2025-06-24 09:32:55.136977

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7f2a41338d41"
down_revision = "2f4e8294d927"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "FinancialConversation", sa.Column("operator", sa.String(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("FinancialConversation", "operator")
    # ### end Alembic commands ###
