"""Add conversion_factor and relationship to FinancialConversation

Revision ID: 2f4e8294d927
Revises: 6d7e2f4d865b
Create Date: 2025-06-19 09:16:11.009540

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2f4e8294d927"
down_revision = "6d7e2f4d865b"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "FinancialConversation",
        sa.Column("conversion_factor", sa.Float(), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("FinancialConversation", "conversion_factor")
    # ### end Alembic commands ###
