from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from starlette.requests import Request

from app.cli import downgrade_database, init_database, upgrade_database

database_router = APIRouter()


@database_router.post("/upgrade")
async def upgrade_database_api(
    request: Request,
    tag: str = None,
    sql: bool = False,
    revision: str = "head",
):
    try:
        # Construct the command with the relevant options
        runner = CliRunner()
        command_args = ["--revision", revision]

        if tag:
            command_args.extend(["--tag", tag])
        if sql:
            command_args.append("--sql")

        result = runner.invoke(upgrade_database, command_args)

        if result.exit_code != 0:
            return JSONResponse(content={"error": result.output}, status_code=400)

        return JSONResponse(
            content={"message": result.output, "status": "success"}, status_code=200
        )
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@database_router.post("/downgrade")
async def downgrade_database_api(
    request: Request,
    tag: str = None,
    sql: bool = False,
    revision: str = "-1",
):
    try:
        # Construct the command with the relevant options for downgrade
        runner = CliRunner()
        command_args = ["--revision", revision]

        if tag:
            command_args.extend(["--tag", tag])
        if sql:
            command_args.append("--sql")

        result = runner.invoke(downgrade_database, command_args)

        if result.exit_code != 0:
            return JSONResponse(content={"error": result.output}, status_code=400)
        return JSONResponse(
            content={"message": result.output, "status": "success"},
            status_code=200,
        )
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@database_router.post("/init")
async def init_database_api(request: Request):
    try:
        # Construct the command with the relevant options for downgrade
        runner = CliRunner()
        command_args = []

        result = runner.invoke(init_database, command_args)

        if result.exit_code != 0:
            return JSONResponse(content={"error": result.output}, status_code=400)
        return JSONResponse(
            content={"message": result.output, "status": "success"},
            status_code=200,
        )
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
