[project]
name = "ai-intranet-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "==3.11.11"

dependencies = [
    "aiohttp==3.11.11",
    "alembic==1.13.1",
    "azure-ai-documentintelligence==1.0.0",
    "azure-ai-formrecognizer==3.3.3",
    "azure-core==1.32.0",
    "azure-identity==1.19.0",
    "azure-keyvault-secrets==4.8.0",
    "azure-search-documents==11.4.0",
    "azure-storage-blob==12.24.0",
    "fastapi==0.115.5",
    "fastapi-socketio==0.0.10",
    "jinja2==3.1.5",
    "langchain==0.3.18",
    "langchain-community==0.3.17",
    "langchain-core==0.3.49",
    "langchain-openai==0.3.11",
    "langfuse==2.57.0",
    "langgraph==0.3.14",
    "langgraph-checkpoint>=2.0.9",
    "langgraph-checkpoint-postgres==2.0.9",
    "langgraph-reflection>=0.0.1",
    "loguru==0.7.3",
    "openpyxl==3.1.5",
    "pandas==2.2.3",
    "pre-commit==3.7.0",
    "pillow==11.1.0",
    "psycopg2-binary==2.9.10",
    "pydantic==2.9.2",
    "pymupdf==1.25.1",
    "pymysql==1.1.0",
    "pyjwt==2.10.1",
    "python-dotenv==1.0.1",
    "python-multipart==0.0.19",
    "setuptools==75.6.0",
    "SQLAlchemy==2.0.36",
    "sqlalchemy-filters==0.13.0",
    "tabulate==0.9.0",
    "uvicorn==0.23.2",
    "xlrd==2.0.1"
]
